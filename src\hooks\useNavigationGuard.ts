"use client"

import { useEffect, useCallback } from "react"
import { useNavigate, useLocation } from "react-router-dom"
import { useAuth } from "../contexts/AuthContext"

interface NavigationGuardOptions {
  requireAuth?: boolean
  requiredRoles?: string[]
  redirectTo?: string
  onUnauthorized?: () => void
}

export const useNavigationGuard = (options: NavigationGuardOptions = {}) => {
  const { requireAuth = true, requiredRoles = [], redirectTo = "/login", onUnauthorized } = options

  const { state } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()

  const checkAccess = useCallback(() => {
    // Check if authentication is required
    if (requireAuth && !state.token) {
      onUnauthorized?.()
      navigate(redirectTo, {
        replace: true,
        state: { from: location.pathname },
      })
      return false
    }

    // Check role requirements
    if (requiredRoles.length > 0 && state.user) {
      const hasRequiredRole = requiredRoles.includes(state.user.role)
      if (!hasRequiredRole) {
        onUnauthorized?.()
        navigate("/dashboard", { replace: true })
        return false
      }
    }

    return true
  }, [requireAuth, requiredRoles, state.token, state.user, navigate, redirectTo, location.pathname, onUnauthorized])

  useEffect(() => {
    checkAccess()
  }, [checkAccess])

  return { hasAccess: checkAccess() }
}
