<?php
/**
 * Simple autoloader for PHP classes
 */

spl_autoload_register(function ($className) {
    // Convert namespace to directory structure
    $className = str_replace('\\', DIRECTORY_SEPARATOR, $className);
    
    // Define possible directories to search for classes
    $directories = [
        __DIR__ . '/../classes/',
        __DIR__ . '/../models/',
        __DIR__ . '/../controllers/',
        __DIR__ . '/../utils/',
    ];
    
    foreach ($directories as $directory) {
        $file = $directory . $className . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Include utility functions
require_once __DIR__ . '/functions.php';
?>
