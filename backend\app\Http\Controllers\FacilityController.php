<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Facility;
use App\Models\User;
use App\Models\ActivityLog;

class FacilityController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * Get all facilities with optional filtering
     */
    public function index(Request $request)
    {
        $query = Facility::query();

        // Search functionality
        if ($request->has('search')) {
            $query->search($request->search);
        }

        // Filter by type
        if ($request->has('type') && $request->type !== 'all') {
            $query->byType($request->type);
        }

        // Filter by service
        if ($request->has('service')) {
            $query->withService($request->service);
        }

        // Filter by location (within radius)
        if ($request->has('latitude') && $request->has('longitude')) {
            $radius = $request->get('radius', 10); // Default 10km
            $query->withinRadius($request->latitude, $request->longitude, $radius);
        }

        // Only verified facilities for non-admin users
        $user = auth()->user();
        if ($user->role !== 'admin') {
            $query->verified();
        }

        // Pagination
        $perPage = $request->get('per_page', 15);
        $facilities = $query->orderBy('name')
                           ->paginate($perPage);

        // Add distance calculation if coordinates provided
        if ($request->has('latitude') && $request->has('longitude')) {
            $facilities->getCollection()->transform(function ($facility) use ($request) {
                $facility->distance = $facility->getDistanceFrom(
                    $request->latitude, 
                    $request->longitude
                );
                return $facility;
            });
        }

        return response()->json($facilities);
    }

    /**
     * Create a new facility (Admin only)
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        
        if ($user->role !== 'admin') {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|in:hospital,clinic,pharmacy,laboratory,specialist',
            'description' => 'string|max:1000',
            'address' => 'required|array',
            'address.street' => 'required|string',
            'address.city' => 'required|string',
            'address.state' => 'required|string',
            'address.zipCode' => 'required|string',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|unique:facilities',
            'website' => 'url|nullable',
            'services' => 'required|array',
            'operatingHours' => 'required|array',
            'coordinates' => 'array',
            'coordinates.lat' => 'numeric|between:-90,90',
            'coordinates.lng' => 'numeric|between:-180,180',
            'licenseNumber' => 'string|max:100',
            'emergencyServices' => 'boolean',
            'parkingAvailable' => 'boolean',
            'wheelchairAccessible' => 'boolean',
            'insuranceAccepted' => 'array',
            'languages' => 'array',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $facility = Facility::create([
            'name' => $request->name,
            'type' => $request->type,
            'description' => $request->description,
            'address' => $request->address,
            'phone' => $request->phone,
            'email' => $request->email,
            'website' => $request->website,
            'services' => $request->services,
            'operatingHours' => $request->operatingHours,
            'coordinates' => $request->coordinates,
            'rating' => 0,
            'reviewCount' => 0,
            'verified' => false,
            'licenseNumber' => $request->licenseNumber,
            'emergencyServices' => $request->get('emergencyServices', false),
            'parkingAvailable' => $request->get('parkingAvailable', false),
            'wheelchairAccessible' => $request->get('wheelchairAccessible', false),
            'insuranceAccepted' => $request->get('insuranceAccepted', []),
            'languages' => $request->get('languages', []),
            'createdAt' => now(),
            'updatedAt' => now(),
        ]);

        // Log facility creation
        ActivityLog::logActivity(
            'facility_created',
            "New facility '{$facility->name}' created",
            [
                'facility_id' => $facility->_id,
                'facility_name' => $facility->name,
                'facility_type' => $facility->type,
            ]
        );

        return response()->json($facility, 201);
    }

    /**
     * Get specific facility
     */
    public function show($id)
    {
        $facility = Facility::find($id);

        if (!$facility) {
            return response()->json(['error' => 'Facility not found'], 404);
        }

        // Include additional data
        $facility->load('doctors');
        $facility->stats = $facility->getStats();
        $facility->available_doctors = $facility->available_doctors;

        return response()->json($facility);
    }

    /**
     * Update facility (Admin only)
     */
    public function update(Request $request, $id)
    {
        $user = auth()->user();
        
        if ($user->role !== 'admin') {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $facility = Facility::find($id);

        if (!$facility) {
            return response()->json(['error' => 'Facility not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'string|max:255',
            'type' => 'in:hospital,clinic,pharmacy,laboratory,specialist',
            'description' => 'string|max:1000',
            'address' => 'array',
            'phone' => 'string|max:20',
            'email' => 'email|unique:facilities,email,' . $id,
            'website' => 'url|nullable',
            'services' => 'array',
            'operatingHours' => 'array',
            'coordinates' => 'array',
            'coordinates.lat' => 'numeric|between:-90,90',
            'coordinates.lng' => 'numeric|between:-180,180',
            'licenseNumber' => 'string|max:100',
            'emergencyServices' => 'boolean',
            'parkingAvailable' => 'boolean',
            'wheelchairAccessible' => 'boolean',
            'insuranceAccepted' => 'array',
            'languages' => 'array',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $oldData = $facility->toArray();
        $facility->update($request->only([
            'name', 'type', 'description', 'address', 'phone', 'email', 'website',
            'services', 'operatingHours', 'coordinates', 'licenseNumber',
            'emergencyServices', 'parkingAvailable', 'wheelchairAccessible',
            'insuranceAccepted', 'languages'
        ]));

        // Log facility update
        ActivityLog::logActivity(
            'facility_updated',
            "Facility '{$facility->name}' updated",
            [
                'facility_id' => $facility->_id,
                'old_data' => $oldData,
                'new_data' => $facility->toArray(),
            ]
        );

        return response()->json($facility);
    }

    /**
     * Delete facility (Admin only)
     */
    public function destroy($id)
    {
        $user = auth()->user();
        
        if ($user->role !== 'admin') {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $facility = Facility::find($id);

        if (!$facility) {
            return response()->json(['error' => 'Facility not found'], 404);
        }

        // Check if facility has active appointments
        $activeAppointments = $facility->appointments()
            ->where('status', 'scheduled')
            ->where('date', '>=', now())
            ->count();

        if ($activeAppointments > 0) {
            return response()->json([
                'error' => 'Cannot delete facility with active appointments'
            ], 400);
        }

        // Log facility deletion
        ActivityLog::logActivity(
            'facility_deleted',
            "Facility '{$facility->name}' deleted",
            [
                'facility_id' => $facility->_id,
                'facility_name' => $facility->name,
            ]
        );

        $facility->delete();

        return response()->json(['message' => 'Facility deleted successfully']);
    }

    /**
     * Verify facility (Admin only)
     */
    public function verify($id)
    {
        $user = auth()->user();
        
        if ($user->role !== 'admin') {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $facility = Facility::find($id);

        if (!$facility) {
            return response()->json(['error' => 'Facility not found'], 404);
        }

        $facility->update(['verified' => true]);

        // Log facility verification
        ActivityLog::logActivity(
            'facility_verified',
            "Facility '{$facility->name}' verified",
            [
                'facility_id' => $facility->_id,
                'facility_name' => $facility->name,
            ]
        );

        return response()->json(['message' => 'Facility verified successfully']);
    }

    /**
     * Get doctors at a facility
     */
    public function getDoctors($id)
    {
        $facility = Facility::find($id);

        if (!$facility) {
            return response()->json(['error' => 'Facility not found'], 404);
        }

        $doctors = $facility->doctors()
            ->where('verified', true)
            ->get()
            ->map(function ($doctor) {
                return [
                    'id' => $doctor->_id,
                    'name' => $doctor->firstName . ' ' . $doctor->lastName,
                    'specialization' => $doctor->specialization,
                    'email' => $doctor->email,
                    'phone' => $doctor->phone,
                    'verified' => $doctor->verified,
                ];
            });

        return response()->json($doctors);
    }

    /**
     * Add review to facility
     */
    public function addReview(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'rating' => 'required|integer|min:1|max:5',
            'review' => 'string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $facility = Facility::find($id);

        if (!$facility) {
            return response()->json(['error' => 'Facility not found'], 404);
        }

        $user = auth()->user();
        
        $facility->addReview($request->rating, $request->review, $user->_id);

        // Log review addition
        ActivityLog::logActivity(
            'facility_review_added',
            "Review added for facility '{$facility->name}'",
            [
                'facility_id' => $facility->_id,
                'rating' => $request->rating,
                'reviewer_id' => $user->_id,
            ]
        );

        return response()->json([
            'message' => 'Review added successfully',
            'new_rating' => $facility->fresh()->rating,
            'review_count' => $facility->fresh()->reviewCount,
        ]);
    }

    /**
     * Get facility types
     */
    public function getTypes()
    {
        return response()->json([
            'hospital' => 'Hospital',
            'clinic' => 'Clinic',
            'pharmacy' => 'Pharmacy',
            'laboratory' => 'Laboratory',
            'specialist' => 'Specialist Center',
        ]);
    }

    /**
     * Search facilities by location
     */
    public function searchByLocation(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'radius' => 'integer|min:1|max:100',
            'type' => 'string',
            'service' => 'string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $query = Facility::verified()
            ->withinRadius(
                $request->latitude, 
                $request->longitude, 
                $request->get('radius', 10)
            );

        if ($request->has('type')) {
            $query->byType($request->type);
        }

        if ($request->has('service')) {
            $query->withService($request->service);
        }

        $facilities = $query->get()
            ->map(function ($facility) use ($request) {
                $facility->distance = $facility->getDistanceFrom(
                    $request->latitude, 
                    $request->longitude
                );
                return $facility;
            })
            ->sortBy('distance')
            ->values();

        return response()->json($facilities);
    }
}
