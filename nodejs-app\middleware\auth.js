import jwt from 'jsonwebtoken';
import User from '../models/User.js';
import logger from '../config/logger.js';

// Middleware to verify JWT token
export const authenticate = async (req, res, next) => {
    try {
        const authHeader = req.header('Authorization');
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                message: 'Access denied. No token provided or invalid format.'
            });
        }
        
        const token = authHeader.substring(7); // Remove 'Bearer ' prefix
        
        if (!token) {
            return res.status(401).json({
                success: false,
                message: 'Access denied. No token provided.'
            });
        }
        
        // Verify token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Find user and check if still exists and is active
        const user = await User.findById(decoded.id).select('-password');
        
        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'Token is not valid. User not found.'
            });
        }
        
        if (user.status !== 'active') {
            return res.status(401).json({
                success: false,
                message: 'Account is not active.'
            });
        }
        
        // Add user to request object
        req.user = user;
        
        // Log authentication
        logger.logAuth('token_verified', user._id, {
            ip: req.ip,
            userAgent: req.get('User-Agent')
        });
        
        next();
    } catch (error) {
        logger.error('Authentication error:', error);
        
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                success: false,
                message: 'Token is not valid.'
            });
        }
        
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                success: false,
                message: 'Token has expired.'
            });
        }
        
        res.status(500).json({
            success: false,
            message: 'Server error during authentication.'
        });
    }
};

// Middleware to check user roles
export const authorize = (...roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Access denied. Please authenticate first.'
            });
        }
        
        if (!roles.includes(req.user.role)) {
            logger.logAuth('unauthorized_access_attempt', req.user._id, {
                requiredRoles: roles,
                userRole: req.user.role,
                endpoint: req.originalUrl,
                method: req.method
            });
            
            return res.status(403).json({
                success: false,
                message: 'Access denied. Insufficient permissions.'
            });
        }
        
        next();
    };
};

// Middleware to check if user owns resource or has admin privileges
export const authorizeOwnerOrAdmin = (resourceUserField = 'user') => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Access denied. Please authenticate first.'
            });
        }
        
        // Admin can access any resource
        if (req.user.role === 'admin') {
            return next();
        }
        
        // Check if user owns the resource
        const resourceUserId = req.resource?.[resourceUserField]?.toString() || 
                              req.params.userId || 
                              req.body.userId;
        
        if (resourceUserId && resourceUserId === req.user._id.toString()) {
            return next();
        }
        
        return res.status(403).json({
            success: false,
            message: 'Access denied. You can only access your own resources.'
        });
    };
};

// Middleware to check if account is locked
export const checkAccountLock = async (req, res, next) => {
    try {
        if (req.user && req.user.isLocked) {
            const lockTime = new Date(req.user.lockUntil);
            const now = new Date();
            const remainingTime = Math.ceil((lockTime - now) / (1000 * 60)); // minutes
            
            return res.status(423).json({
                success: false,
                message: `Account is temporarily locked due to multiple failed login attempts. Try again in ${remainingTime} minutes.`,
                lockUntil: lockTime
            });
        }
        
        next();
    } catch (error) {
        logger.error('Account lock check error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error during account verification.'
        });
    }
};

// Optional authentication - doesn't fail if no token provided
export const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.header('Authorization');
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return next(); // Continue without authentication
        }
        
        const token = authHeader.substring(7);
        
        if (!token) {
            return next(); // Continue without authentication
        }
        
        // Verify token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Find user
        const user = await User.findById(decoded.id).select('-password');
        
        if (user && user.status === 'active') {
            req.user = user;
        }
        
        next();
    } catch (error) {
        // Log error but continue without authentication
        logger.warn('Optional authentication failed:', error.message);
        next();
    }
};

// Middleware to refresh token if it's about to expire
export const refreshTokenIfNeeded = (req, res, next) => {
    if (req.user) {
        const authHeader = req.header('Authorization');
        const token = authHeader.substring(7);
        
        try {
            const decoded = jwt.decode(token);
            const now = Math.floor(Date.now() / 1000);
            const timeUntilExpiry = decoded.exp - now;
            
            // If token expires in less than 15 minutes, include new token in response
            if (timeUntilExpiry < 900) { // 15 minutes
                const newToken = req.user.generateAuthToken();
                res.set('X-New-Token', newToken);
            }
        } catch (error) {
            // Ignore errors in token refresh
            logger.warn('Token refresh check failed:', error.message);
        }
    }
    
    next();
};

export default {
    authenticate,
    authorize,
    authorizeOwnerOrAdmin,
    checkAccountLock,
    optionalAuth,
    refreshTokenIfNeeded
};
