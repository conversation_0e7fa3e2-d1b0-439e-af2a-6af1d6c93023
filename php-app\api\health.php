<?php
/**
 * Health check endpoint
 */

try {
    // Check database connection
    $db = Database::getInstance();
    $dbStatus = 'connected';
    
    // Test database ping
    $db->getDatabase()->command(['ping' => 1]);
    
} catch (Exception $e) {
    $dbStatus = 'disconnected';
    logMessage("Health check - Database error: " . $e->getMessage(), 'error');
}

$response = [
    'status' => 'ok',
    'timestamp' => date('c'),
    'app_name' => APP_NAME,
    'environment' => APP_ENV,
    'php_version' => PHP_VERSION,
    'database' => [
        'type' => 'MongoDB',
        'status' => $dbStatus
    ],
    'memory_usage' => [
        'current' => memory_get_usage(true),
        'peak' => memory_get_peak_usage(true)
    ]
];

sendJsonResponse($response);
?>
