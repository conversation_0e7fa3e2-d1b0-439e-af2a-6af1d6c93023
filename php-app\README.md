# AfyaSecure PHP - Health Management System

A modern PHP application with MongoDB Atlas integration for healthcare management.

## 🚀 Features

- **Pure PHP** - No framework dependencies
- **MongoDB Atlas** - Cloud database integration
- **RESTful API** - Clean API endpoints
- **User Management** - Role-based access control
- **Patient Management** - Comprehensive patient records
- **Security** - JWT authentication, password hashing
- **CORS Support** - Frontend integration ready
- **Error Logging** - Comprehensive logging system

## 📋 Requirements

- PHP 8.2 or higher
- MongoDB PHP Extension
- XAMPP (for local development)
- MongoDB Atlas account
- Composer (for dependency management)

## 🛠️ Installation

### 1. Install MongoDB PHP Extension

For XAMPP on Windows:
```bash
# Download the MongoDB PHP extension from:
# https://pecl.php.net/package/mongodb

# Add to your php.ini file:
extension=mongodb
```

### 2. Install Composer Dependencies

```bash
cd php-app
composer install
```

### 3. Configure Environment

1. Copy `.env.example` to `.env` (if needed)
2. Update your MongoDB Atlas connection string in `.env`:

```env
MONGODB_URI="mongodb+srv://username:<EMAIL>/?retryWrites=true&w=majority"
MONGODB_DATABASE=afyasecure
```

### 4. Set Up XAMPP

1. Copy the `php-app` folder to your XAMPP `htdocs` directory
2. Start Apache in XAMPP Control Panel
3. Access the application at: `http://localhost/php-app`

## 🔧 Configuration

### MongoDB Atlas Setup

1. Create a MongoDB Atlas account at https://cloud.mongodb.com
2. Create a new cluster
3. Create a database user
4. Whitelist your IP address
5. Get your connection string and update the `.env` file

### Environment Variables

Key environment variables in `.env`:

```env
# Application
APP_NAME="AfyaSecure PHP"
APP_ENV=development
APP_DEBUG=true

# Database
MONGODB_URI="your-mongodb-atlas-connection-string"
MONGODB_DATABASE=afyasecure

# Security
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRY=3600

# CORS
CORS_ALLOWED_ORIGINS="http://localhost:3000,http://127.0.0.1:3000"
```

## 📚 API Endpoints

### Health Check
```
GET /php-app/api/health
```

### Users
```
GET    /php-app/api/users          # Get all users
POST   /php-app/api/users          # Create user
PUT    /php-app/api/users          # Update user
DELETE /php-app/api/users          # Delete user
```

### Patients
```
GET    /php-app/api/patients       # Get all patients
POST   /php-app/api/patients       # Create patient
PUT    /php-app/api/patients       # Update patient
DELETE /php-app/api/patients       # Delete patient (soft delete)
```

## 📝 API Usage Examples

### Create a User
```bash
curl -X POST http://localhost/php-app/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Dr. John Doe",
    "email": "<EMAIL>",
    "password": "securepassword",
    "role": "doctor"
  }'
```

### Create a Patient
```bash
curl -X POST http://localhost/php-app/api/patients \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "Jane",
    "last_name": "Smith",
    "date_of_birth": "1990-05-15",
    "gender": "female",
    "email": "<EMAIL>",
    "phone": "+**********"
  }'
```

### Get Patients with Search
```bash
curl "http://localhost/php-app/api/patients?search=jane&limit=5"
```

## 🗂️ Project Structure

```
php-app/
├── api/                    # API endpoints
│   ├── health.php         # Health check
│   ├── users.php          # User management
│   └── patients.php       # Patient management
├── config/                # Configuration files
│   └── config.php         # Main configuration
├── includes/              # Core includes
│   ├── autoloader.php     # Class autoloader
│   ├── database.php       # MongoDB connection
│   └── functions.php      # Utility functions
├── pages/                 # Web pages
│   ├── home.php          # Homepage
│   └── 404.php           # 404 error page
├── logs/                  # Log files
├── uploads/               # File uploads
├── .env                   # Environment variables
├── index.php             # Main entry point
└── README.md             # This file
```

## 🔒 Security Features

- **Password Hashing** - Bcrypt with cost factor 12
- **JWT Authentication** - Secure token-based auth
- **Input Sanitization** - XSS protection
- **CORS Configuration** - Controlled cross-origin access
- **Rate Limiting** - API abuse prevention
- **Soft Deletes** - Data preservation

## 🐛 Debugging

### Enable Debug Mode
Set in `.env`:
```env
APP_DEBUG=true
APP_ENV=development
```

### Check Logs
```bash
tail -f php-app/logs/app.log
```

### Test Database Connection
Visit: `http://localhost/php-app/api/health`

## 🚀 Deployment

For production deployment:

1. Set `APP_ENV=production` and `APP_DEBUG=false`
2. Use strong JWT secrets
3. Configure proper CORS origins
4. Set up SSL/HTTPS
5. Configure MongoDB Atlas IP whitelist
6. Set up proper error logging

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
