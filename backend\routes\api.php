<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\PatientController;
use App\Http\Controllers\AppointmentController;
use App\Http\Controllers\FacilityController;
use App\Http\Controllers\BillingController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\DashboardController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// API versioning
Route::prefix('v1')->group(function () {
    
    // Authentication routes
    Route::prefix('auth')->group(function () {
        Route::post('login', [AuthController::class, 'login']);
        Route::post('register', [AuthController::class, 'register']);
        Route::post('refresh', [AuthController::class, 'refresh']);
        Route::post('logout', [AuthController::class, 'logout']);
        Route::get('me', [AuthController::class, 'me']);
        Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
        Route::post('reset-password', [AuthController::class, 'resetPassword']);
        Route::post('verify-qr', [AuthController::class, 'verifyQRCode']);
    });

    // Protected routes
    Route::middleware('auth:api')->group(function () {
        
        // Dashboard
        Route::get('dashboard/stats', [DashboardController::class, 'getStats']);
        
        // Patient records (doctors and admins only)
        Route::middleware('role:doctor,admin')->group(function () {
            Route::apiResource('patients', PatientController::class);
            Route::post('patients/{id}/visit-notes', [PatientController::class, 'addVisitNote']);
            Route::post('patients/{id}/prescriptions', [PatientController::class, 'addPrescription']);
            Route::post('patients/{id}/documents', [PatientController::class, 'uploadDocument']);
        });
        
        // Appointments
        Route::apiResource('appointments', AppointmentController::class);
        Route::patch('appointments/{id}/status', [AppointmentController::class, 'updateStatus']);
        Route::get('appointments/calendar/{date}', [AppointmentController::class, 'getCalendar']);
        
        // Health facilities
        Route::apiResource('facilities', FacilityController::class);
        Route::get('facilities/search/{query}', [FacilityController::class, 'search']);
        Route::get('facilities/types', [FacilityController::class, 'getTypes']);
        Route::get('facilities/search-location', [FacilityController::class, 'searchByLocation']);
        Route::get('facilities/{id}/doctors', [FacilityController::class, 'getDoctors']);
        Route::post('facilities/{id}/review', [FacilityController::class, 'addReview']);
        Route::post('facilities/{id}/verify', [FacilityController::class, 'verify']);
        
        // Billing (doctors and admins only)
        Route::middleware('role:doctor,admin')->group(function () {
            Route::apiResource('billing', BillingController::class);
            Route::post('billing/{id}/approve', [BillingController::class, 'approve']);
            Route::post('billing/{id}/submit', [BillingController::class, 'submit']);
            Route::post('billing/{id}/pay', [BillingController::class, 'markAsPaid']);
            Route::get('billing/{id}/invoice', [BillingController::class, 'generateInvoice']);
            Route::get('billing/stats/overview', [BillingController::class, 'getStats']);
            Route::get('billing/overdue/list', [BillingController::class, 'getOverdue']);
        });
        
        // Admin routes
        Route::middleware('role:admin')->group(function () {
            Route::prefix('admin')->group(function () {
                Route::get('stats', [AdminController::class, 'getStats']);
                Route::get('users', [AdminController::class, 'getUsers']);
                Route::post('users', [AdminController::class, 'createUser']);
                Route::put('users/{id}', [AdminController::class, 'updateUser']);
                Route::delete('users/{id}', [AdminController::class, 'deleteUser']);
                Route::post('users/{id}/verify', [AdminController::class, 'verifyUser']);
                Route::post('users/{id}/suspend', [AdminController::class, 'suspendUser']);
                Route::get('activity-logs', [AdminController::class, 'getActivityLogs']);
                Route::get('system-health', [AdminController::class, 'getSystemHealth']);
                Route::post('clean-logs', [AdminController::class, 'cleanLogs']);
            });
        });
        
        // eVerification
        Route::get('verify/{qrCode}', [AuthController::class, 'verifyQRCode']);
        Route::get('profile/qr', [AuthController::class, 'getQRCode']);
        
        // Profile management
        Route::get('profile', [AuthController::class, 'me']);
        Route::put('profile', [AuthController::class, 'updateProfile']);
        Route::post('profile/photo', [AuthController::class, 'uploadPhoto']);
    });
});

// Health check endpoint
Route::get('health', function () {
    return response()->json([
        'status' => 'healthy',
        'timestamp' => now(),
        'version' => '1.0.0'
    ]);
});
