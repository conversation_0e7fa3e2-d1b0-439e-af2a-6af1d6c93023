<?php
/**
 * Patients API endpoint
 */

$method = $_SERVER['REQUEST_METHOD'];
$db = Database::getInstance();

switch ($method) {
    case 'GET':
        handleGetPatients($db);
        break;
    
    case 'POST':
        handleCreatePatient($db);
        break;
    
    case 'PUT':
        handleUpdatePatient($db);
        break;
    
    case 'DELETE':
        handleDeletePatient($db);
        break;
    
    default:
        sendJsonResponse(['error' => 'Method not allowed'], 405);
        break;
}

function handleGetPatients($db) {
    try {
        // Get query parameters
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
        $skip = isset($_GET['skip']) ? (int)$_GET['skip'] : 0;
        $search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
        
        // Build filter
        $filter = [];
        if (!empty($search)) {
            $filter['$or'] = [
                ['first_name' => new MongoDB\BSON\Regex($search, 'i')],
                ['last_name' => new MongoDB\BSON\Regex($search, 'i')],
                ['email' => new MongoDB\BSON\Regex($search, 'i')],
                ['phone' => new MongoDB\BSON\Regex($search, 'i')]
            ];
        }
        
        // Get patients
        $patients = $db->find('patients', $filter, [
            'limit' => $limit,
            'skip' => $skip,
            'sort' => ['created_at' => -1]
        ]);
        
        $patientList = [];
        foreach ($patients as $patient) {
            $patientList[] = $patient;
        }
        
        sendJsonResponse([
            'success' => true,
            'data' => $patientList,
            'count' => count($patientList)
        ]);
        
    } catch (Exception $e) {
        logMessage("Get patients error: " . $e->getMessage(), 'error');
        sendJsonResponse(['error' => 'Failed to fetch patients'], 500);
    }
}

function handleCreatePatient($db) {
    try {
        $input = getJsonInput();
        
        // Validate required fields
        $required = ['first_name', 'last_name', 'date_of_birth', 'gender'];
        $missing = validateRequired($input, $required);
        
        if (!empty($missing)) {
            sendJsonResponse([
                'error' => 'Missing required fields',
                'missing_fields' => $missing
            ], 400);
        }
        
        // Sanitize input
        $patientData = sanitizeInput($input);
        
        // Validate email if provided
        if (isset($patientData['email']) && !empty($patientData['email'])) {
            if (!filter_var($patientData['email'], FILTER_VALIDATE_EMAIL)) {
                sendJsonResponse(['error' => 'Invalid email format'], 400);
            }
        }
        
        // Validate date of birth
        $dob = DateTime::createFromFormat('Y-m-d', $patientData['date_of_birth']);
        if (!$dob) {
            sendJsonResponse(['error' => 'Invalid date of birth format. Use YYYY-MM-DD'], 400);
        }
        
        // Add metadata
        $patientData['id'] = generateUUID();
        $patientData['patient_number'] = 'P' . date('Y') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        $patientData['created_at'] = new MongoDB\BSON\UTCDateTime();
        $patientData['updated_at'] = new MongoDB\BSON\UTCDateTime();
        $patientData['status'] = 'active';
        
        // Initialize medical history
        $patientData['medical_history'] = [];
        $patientData['allergies'] = [];
        $patientData['medications'] = [];
        $patientData['emergency_contacts'] = [];
        
        // Insert patient
        $result = $db->insertOne('patients', $patientData);
        
        if ($result) {
            sendJsonResponse([
                'success' => true,
                'message' => 'Patient created successfully',
                'data' => $patientData
            ], 201);
        } else {
            sendJsonResponse(['error' => 'Failed to create patient'], 500);
        }
        
    } catch (Exception $e) {
        logMessage("Create patient error: " . $e->getMessage(), 'error');
        sendJsonResponse(['error' => 'Failed to create patient'], 500);
    }
}

function handleUpdatePatient($db) {
    try {
        $input = getJsonInput();
        
        if (!isset($input['id'])) {
            sendJsonResponse(['error' => 'Patient ID is required'], 400);
        }
        
        $patientId = sanitizeInput($input['id']);
        unset($input['id']);
        
        // Sanitize input
        $updateData = sanitizeInput($input);
        
        // Validate email if provided
        if (isset($updateData['email']) && !empty($updateData['email'])) {
            if (!filter_var($updateData['email'], FILTER_VALIDATE_EMAIL)) {
                sendJsonResponse(['error' => 'Invalid email format'], 400);
            }
        }
        
        // Validate date of birth if provided
        if (isset($updateData['date_of_birth'])) {
            $dob = DateTime::createFromFormat('Y-m-d', $updateData['date_of_birth']);
            if (!$dob) {
                sendJsonResponse(['error' => 'Invalid date of birth format. Use YYYY-MM-DD'], 400);
            }
        }
        
        // Add updated timestamp
        $updateData['updated_at'] = new MongoDB\BSON\UTCDateTime();
        
        // Update patient
        $result = $db->updateOne('patients', 
            ['id' => $patientId], 
            ['$set' => $updateData]
        );
        
        if ($result->getModifiedCount() > 0) {
            sendJsonResponse([
                'success' => true,
                'message' => 'Patient updated successfully'
            ]);
        } else {
            sendJsonResponse(['error' => 'Patient not found or no changes made'], 404);
        }
        
    } catch (Exception $e) {
        logMessage("Update patient error: " . $e->getMessage(), 'error');
        sendJsonResponse(['error' => 'Failed to update patient'], 500);
    }
}

function handleDeletePatient($db) {
    try {
        $input = getJsonInput();
        
        if (!isset($input['id'])) {
            sendJsonResponse(['error' => 'Patient ID is required'], 400);
        }
        
        $patientId = sanitizeInput($input['id']);
        
        // Soft delete - update status instead of actual deletion
        $result = $db->updateOne('patients', 
            ['id' => $patientId], 
            ['$set' => [
                'status' => 'deleted',
                'deleted_at' => new MongoDB\BSON\UTCDateTime(),
                'updated_at' => new MongoDB\BSON\UTCDateTime()
            ]]
        );
        
        if ($result->getModifiedCount() > 0) {
            sendJsonResponse([
                'success' => true,
                'message' => 'Patient deleted successfully'
            ]);
        } else {
            sendJsonResponse(['error' => 'Patient not found'], 404);
        }
        
    } catch (Exception $e) {
        logMessage("Delete patient error: " . $e->getMessage(), 'error');
        sendJsonResponse(['error' => 'Failed to delete patient'], 500);
    }
}
?>
