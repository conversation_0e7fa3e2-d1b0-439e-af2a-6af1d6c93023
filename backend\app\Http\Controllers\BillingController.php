<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Billing;
use App\Models\User;
use App\Models\Appointment;
use App\Models\Facility;
use App\Models\ActivityLog;

class BillingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * Get bills based on user role
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $query = Billing::query();

        // Filter based on user role
        switch ($user->role) {
            case 'patient':
                $query->where('patientId', $user->_id);
                break;
            case 'doctor':
                $query->where('doctorId', $user->_id);
                break;
            case 'admin':
                // Admin can see all bills
                break;
        }

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('date_from') && $request->has('date_to')) {
            $query->whereBetween('createdDate', [$request->date_from, $request->date_to]);
        }

        if ($request->has('overdue') && $request->overdue) {
            $query->overdue();
        }

        // Pagination
        $perPage = $request->get('per_page', 15);
        $bills = $query->orderBy('createdDate', 'desc')
                      ->paginate($perPage);

        return response()->json($bills);
    }

    /**
     * Create a new bill
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        
        if (!in_array($user->role, ['doctor', 'admin'])) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'patientId' => 'required|string',
            'doctorId' => 'required|string',
            'facilityId' => 'required|string',
            'appointmentId' => 'string|nullable',
            'items' => 'required|array|min:1',
            'items.*.description' => 'required|string',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unitPrice' => 'required|numeric|min:0',
            'dueDate' => 'required|date|after:today',
            'notes' => 'string|max:1000',
            'discount' => 'numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Verify patient, doctor, and facility exist
        $patient = User::find($request->patientId);
        $doctor = User::find($request->doctorId);
        $facility = Facility::find($request->facilityId);

        if (!$patient || $patient->role !== 'patient') {
            return response()->json(['error' => 'Invalid patient'], 400);
        }

        if (!$doctor || $doctor->role !== 'doctor') {
            return response()->json(['error' => 'Invalid doctor'], 400);
        }

        if (!$facility) {
            return response()->json(['error' => 'Invalid facility'], 400);
        }

        // Verify appointment if provided
        $appointment = null;
        if ($request->appointmentId) {
            $appointment = Appointment::find($request->appointmentId);
            if (!$appointment) {
                return response()->json(['error' => 'Invalid appointment'], 400);
            }
        }

        // Process items and calculate totals
        $items = collect($request->items)->map(function ($item) {
            return [
                'id' => (string) new \MongoDB\BSON\ObjectId(),
                'description' => $item['description'],
                'quantity' => $item['quantity'],
                'unitPrice' => $item['unitPrice'],
                'total' => $item['quantity'] * $item['unitPrice'],
            ];
        })->toArray();

        $subtotal = array_sum(array_column($items, 'total'));
        $taxRate = 0.08; // 8% tax
        $tax = $subtotal * $taxRate;
        $discount = $request->get('discount', 0);
        $total = $subtotal + $tax - $discount;

        $bill = Billing::create([
            'patientId' => $request->patientId,
            'doctorId' => $request->doctorId,
            'facilityId' => $request->facilityId,
            'appointmentId' => $request->appointmentId,
            'patientName' => $patient->firstName . ' ' . $patient->lastName,
            'doctorName' => $doctor->firstName . ' ' . $doctor->lastName,
            'facilityName' => $facility->name,
            'items' => $items,
            'subtotal' => round($subtotal, 2),
            'tax' => round($tax, 2),
            'discount' => $discount,
            'total' => round($total, 2),
            'status' => 'draft',
            'createdDate' => now(),
            'dueDate' => $request->dueDate,
            'notes' => $request->notes,
            'createdAt' => now(),
            'updatedAt' => now(),
        ]);

        // Log bill creation
        ActivityLog::logActivity(
            'bill_created',
            "New bill created for {$patient->firstName} {$patient->lastName}",
            [
                'bill_id' => $bill->_id,
                'patient_id' => $patient->_id,
                'doctor_id' => $doctor->_id,
                'total_amount' => $bill->total,
            ]
        );

        return response()->json($bill, 201);
    }

    /**
     * Get specific bill
     */
    public function show($id)
    {
        $user = auth()->user();
        $bill = Billing::find($id);

        if (!$bill) {
            return response()->json(['error' => 'Bill not found'], 404);
        }

        // Check permissions
        if ($user->role === 'patient' && $bill->patientId !== $user->_id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        if ($user->role === 'doctor' && $bill->doctorId !== $user->_id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        return response()->json($bill);
    }

    /**
     * Update bill
     */
    public function update(Request $request, $id)
    {
        $user = auth()->user();
        
        if (!in_array($user->role, ['doctor', 'admin'])) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $bill = Billing::find($id);

        if (!$bill) {
            return response()->json(['error' => 'Bill not found'], 404);
        }

        // Only allow updates for draft bills
        if ($bill->status !== 'draft') {
            return response()->json(['error' => 'Can only update draft bills'], 400);
        }

        $validator = Validator::make($request->all(), [
            'items' => 'array',
            'items.*.description' => 'required_with:items|string',
            'items.*.quantity' => 'required_with:items|integer|min:1',
            'items.*.unitPrice' => 'required_with:items|numeric|min:0',
            'dueDate' => 'date|after:today',
            'notes' => 'string|max:1000',
            'discount' => 'numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $updateData = $request->only(['dueDate', 'notes', 'discount']);

        if ($request->has('items')) {
            $items = collect($request->items)->map(function ($item) {
                return [
                    'id' => $item['id'] ?? (string) new \MongoDB\BSON\ObjectId(),
                    'description' => $item['description'],
                    'quantity' => $item['quantity'],
                    'unitPrice' => $item['unitPrice'],
                    'total' => $item['quantity'] * $item['unitPrice'],
                ];
            })->toArray();

            $updateData['items'] = $items;
        }

        $bill->update($updateData);
        
        // Recalculate totals if items were updated
        if ($request->has('items') || $request->has('discount')) {
            $bill->calculateTotals();
        }

        // Log bill update
        ActivityLog::logActivity(
            'bill_updated',
            "Bill updated for {$bill->patientName}",
            [
                'bill_id' => $bill->_id,
                'updated_fields' => array_keys($updateData),
            ]
        );

        return response()->json($bill->fresh());
    }

    /**
     * Delete bill
     */
    public function destroy($id)
    {
        $user = auth()->user();
        
        if ($user->role !== 'admin') {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $bill = Billing::find($id);

        if (!$bill) {
            return response()->json(['error' => 'Bill not found'], 404);
        }

        // Only allow deletion of draft bills
        if ($bill->status !== 'draft') {
            return response()->json(['error' => 'Can only delete draft bills'], 400);
        }

        // Log bill deletion
        ActivityLog::logActivity(
            'bill_deleted',
            "Bill deleted for {$bill->patientName}",
            [
                'bill_id' => $bill->_id,
                'patient_id' => $bill->patientId,
                'total_amount' => $bill->total,
            ]
        );

        $bill->delete();

        return response()->json(['message' => 'Bill deleted successfully']);
    }

    /**
     * Approve bill (Admin only)
     */
    public function approve($id)
    {
        $user = auth()->user();
        
        if ($user->role !== 'admin') {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $bill = Billing::find($id);

        if (!$bill) {
            return response()->json(['error' => 'Bill not found'], 404);
        }

        if ($bill->status !== 'pending') {
            return response()->json(['error' => 'Bill is not pending approval'], 400);
        }

        $bill->approve($user->_id);

        return response()->json([
            'message' => 'Bill approved successfully',
            'bill' => $bill->fresh()
        ]);
    }

    /**
     * Submit bill for approval
     */
    public function submit($id)
    {
        $user = auth()->user();
        
        if (!in_array($user->role, ['doctor', 'admin'])) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $bill = Billing::find($id);

        if (!$bill) {
            return response()->json(['error' => 'Bill not found'], 404);
        }

        if ($bill->status !== 'draft') {
            return response()->json(['error' => 'Bill is not in draft status'], 400);
        }

        $bill->update(['status' => 'pending']);

        // Log bill submission
        ActivityLog::logActivity(
            'bill_submitted',
            "Bill submitted for approval for {$bill->patientName}",
            [
                'bill_id' => $bill->_id,
                'submitted_by' => $user->_id,
            ]
        );

        return response()->json([
            'message' => 'Bill submitted for approval',
            'bill' => $bill->fresh()
        ]);
    }

    /**
     * Mark bill as paid
     */
    public function markAsPaid(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'paymentMethod' => 'required|string',
            'paymentReference' => 'string|nullable',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $bill = Billing::find($id);

        if (!$bill) {
            return response()->json(['error' => 'Bill not found'], 404);
        }

        if ($bill->status !== 'approved') {
            return response()->json(['error' => 'Bill must be approved before payment'], 400);
        }

        $bill->markAsPaid($request->paymentMethod, $request->paymentReference);

        return response()->json([
            'message' => 'Bill marked as paid successfully',
            'bill' => $bill->fresh()
        ]);
    }

    /**
     * Generate invoice
     */
    public function generateInvoice($id)
    {
        $user = auth()->user();
        $bill = Billing::find($id);

        if (!$bill) {
            return response()->json(['error' => 'Bill not found'], 404);
        }

        // Check permissions
        if ($user->role === 'patient' && $bill->patientId !== $user->_id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        if ($user->role === 'doctor' && $bill->doctorId !== $user->_id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Generate invoice number if not exists
        if (!$bill->invoiceNumber) {
            $bill->generateInvoiceNumber();
        }

        $invoiceData = $bill->generateInvoiceData();

        // Log invoice generation
        ActivityLog::logActivity(
            'invoice_generated',
            "Invoice generated for bill #{$bill->_id}",
            [
                'bill_id' => $bill->_id,
                'invoice_number' => $bill->invoiceNumber,
            ]
        );

        return response()->json([
            'message' => 'Invoice generated successfully',
            'invoice' => $invoiceData
        ]);
    }

    /**
     * Get billing statistics
     */
    public function getStats(Request $request)
    {
        $user = auth()->user();
        
        if ($user->role !== 'admin') {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');

        $stats = Billing::getStats($startDate, $endDate);

        return response()->json($stats);
    }

    /**
     * Get overdue bills
     */
    public function getOverdue()
    {
        $user = auth()->user();
        
        if ($user->role !== 'admin') {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $overdueBills = Billing::overdue()
            ->orderBy('dueDate')
            ->get();

        return response()->json($overdueBills);
    }
}
