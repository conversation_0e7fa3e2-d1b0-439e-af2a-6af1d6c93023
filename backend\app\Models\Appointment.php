<?php

namespace App\Models;

use Jenssegers\Mongodb\Eloquent\Model;
use Carbon\Carbon;

class Appointment extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'appointments';

    protected $fillable = [
        'patientId',
        'doctorId',
        'facilityId',
        'patientName',
        'doctorName',
        'facilityName',
        'date',
        'time',
        'duration',
        'type',
        'status',
        'reason',
        'notes',
        'symptoms',
        'diagnosis',
        'prescription',
        'followUpDate',
        'reminderSent',
        'createdAt',
        'updatedAt',
    ];

    protected $casts = [
        'date' => 'date',
        'followUpDate' => 'date',
        'reminderSent' => 'boolean',
        'symptoms' => 'array',
        'prescription' => 'array',
        'createdAt' => 'datetime',
        'updatedAt' => 'datetime',
    ];

    /**
     * Get the patient associated with the appointment
     */
    public function patient()
    {
        return $this->belongsTo(User::class, 'patientId', '_id');
    }

    /**
     * Get the doctor associated with the appointment
     */
    public function doctor()
    {
        return $this->belongsTo(User::class, 'doctorId', '_id');
    }

    /**
     * Get the facility associated with the appointment
     */
    public function facility()
    {
        return $this->belongsTo(Facility::class, 'facilityId', '_id');
    }

    /**
     * Scope for upcoming appointments
     */
    public function scopeUpcoming($query)
    {
        return $query->where('date', '>=', now()->toDateString())
                    ->where('status', 'scheduled');
    }

    /**
     * Scope for today's appointments
     */
    public function scopeToday($query)
    {
        return $query->whereDate('date', today());
    }

    /**
     * Scope for appointments by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for appointments by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * Check if appointment is overdue
     */
    public function getIsOverdueAttribute()
    {
        if ($this->status !== 'scheduled') {
            return false;
        }

        $appointmentDateTime = Carbon::parse($this->date . ' ' . $this->time);
        return $appointmentDateTime->isPast();
    }

    /**
     * Get formatted appointment time
     */
    public function getFormattedTimeAttribute()
    {
        return Carbon::parse($this->time)->format('g:i A');
    }

    /**
     * Get appointment duration in minutes
     */
    public function getDurationInMinutesAttribute()
    {
        return $this->duration ?? 30; // Default 30 minutes
    }

    /**
     * Update appointment status
     */
    public function updateStatus($status, $notes = null)
    {
        $this->update([
            'status' => $status,
            'notes' => $notes ? ($this->notes . "\n" . $notes) : $this->notes,
            'updatedAt' => now(),
        ]);

        // Log status change
        ActivityLog::create([
            'user_id' => auth()->id(),
            'action' => 'appointment_status_update',
            'description' => "Appointment status changed to {$status}",
            'metadata' => [
                'appointment_id' => $this->_id,
                'old_status' => $this->getOriginal('status'),
                'new_status' => $status,
            ],
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now(),
        ]);
    }

    /**
     * Send appointment reminder
     */
    public function sendReminder()
    {
        // Implementation for sending reminder
        // This would integrate with your notification system
        
        $this->update(['reminderSent' => true]);
        
        return true;
    }

    /**
     * Check if appointment can be cancelled
     */
    public function canBeCancelled()
    {
        if ($this->status !== 'scheduled') {
            return false;
        }

        $appointmentDateTime = Carbon::parse($this->date . ' ' . $this->time);
        $hoursUntilAppointment = now()->diffInHours($appointmentDateTime, false);

        // Can cancel if appointment is more than 2 hours away
        return $hoursUntilAppointment > 2;
    }

    /**
     * Check if appointment can be rescheduled
     */
    public function canBeRescheduled()
    {
        return $this->canBeCancelled();
    }

    /**
     * Get available time slots for a doctor on a specific date
     */
    public static function getAvailableSlots($doctorId, $date, $duration = 30)
    {
        $workingHours = [
            'start' => '09:00',
            'end' => '17:00',
            'lunch_start' => '12:00',
            'lunch_end' => '13:00',
        ];

        $bookedSlots = self::where('doctorId', $doctorId)
            ->where('date', $date)
            ->where('status', 'scheduled')
            ->pluck('time')
            ->toArray();

        $availableSlots = [];
        $currentTime = Carbon::parse($workingHours['start']);
        $endTime = Carbon::parse($workingHours['end']);
        $lunchStart = Carbon::parse($workingHours['lunch_start']);
        $lunchEnd = Carbon::parse($workingHours['lunch_end']);

        while ($currentTime->lt($endTime)) {
            $timeSlot = $currentTime->format('H:i');
            
            // Skip lunch time
            if ($currentTime->gte($lunchStart) && $currentTime->lt($lunchEnd)) {
                $currentTime->addMinutes($duration);
                continue;
            }

            // Check if slot is not booked
            if (!in_array($timeSlot, $bookedSlots)) {
                $availableSlots[] = $timeSlot;
            }

            $currentTime->addMinutes($duration);
        }

        return $availableSlots;
    }
}
