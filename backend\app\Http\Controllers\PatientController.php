<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\PatientRecord;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class PatientController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
        $this->middleware('role:doctor,admin');
    }

    /**
     * Get all patients with pagination
     */
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 15);
        $search = $request->get('search');

        $query = PatientRecord::query();

        // If doctor, only show their patients
        if (auth()->user()->role === 'doctor') {
            $query->where('doctorId', auth()->id());
        }

        // Search functionality
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('personalInfo.firstName', 'like', "%{$search}%")
                  ->orWhere('personalInfo.lastName', 'like', "%{$search}%")
                  ->orWhere('personalInfo.email', 'like', "%{$search}%");
            });
        }

        $patients = $query->paginate($perPage);

        // Log access
        ActivityLog::create([
            'user_id' => auth()->id(),
            'action' => 'view_patients',
            'description' => 'Accessed patient records list',
            'ip_address' => $request->ip(),
            'timestamp' => now(),
        ]);

        return response()->json($patients);
    }

    /**
     * Get specific patient record
     */
    public function show(Request $request, $id)
    {
        $patient = PatientRecord::find($id);

        if (!$patient) {
            return response()->json(['error' => 'Patient not found'], 404);
        }

        // Check access permissions
        if (auth()->user()->role === 'doctor' && $patient->doctorId !== auth()->id()) {
            return response()->json(['error' => 'Unauthorized access'], 403);
        }

        // Log access
        ActivityLog::create([
            'user_id' => auth()->id(),
            'action' => 'view_patient',
            'description' => "Viewed patient record: {$patient->personalInfo['firstName']} {$patient->personalInfo['lastName']}",
            'resource_id' => $id,
            'ip_address' => $request->ip(),
            'timestamp' => now(),
        ]);

        return response()->json($patient);
    }

    /**
     * Create new patient record
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'personalInfo.firstName' => 'required|string|max:255',
            'personalInfo.lastName' => 'required|string|max:255',
            'personalInfo.email' => 'required|email|unique:patient_records,personalInfo.email',
            'personalInfo.phone' => 'required|string|max:20',
            'personalInfo.dateOfBirth' => 'required|date',
            'personalInfo.gender' => 'required|in:male,female,other',
            'personalInfo.address' => 'required|array',
            'emergencyContact' => 'required|array',
            'insurance' => 'array',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $patientData = $request->all();
        $patientData['doctorId'] = auth()->id();
        $patientData['facilityId'] = auth()->user()->facilityId;
        $patientData['createdAt'] = now();
        $patientData['updatedAt'] = now();

        $patient = PatientRecord::create($patientData);

        // Log creation
        ActivityLog::create([
            'user_id' => auth()->id(),
            'action' => 'create_patient',
            'description' => "Created patient record: {$patientData['personalInfo']['firstName']} {$patientData['personalInfo']['lastName']}",
            'resource_id' => $patient->_id,
            'ip_address' => $request->ip(),
            'timestamp' => now(),
        ]);

        return response()->json($patient, 201);
    }

    /**
     * Update patient record
     */
    public function update(Request $request, $id)
    {
        $patient = PatientRecord::find($id);

        if (!$patient) {
            return response()->json(['error' => 'Patient not found'], 404);
        }

        // Check access permissions
        if (auth()->user()->role === 'doctor' && $patient->doctorId !== auth()->id()) {
            return response()->json(['error' => 'Unauthorized access'], 403);
        }

        $validator = Validator::make($request->all(), [
            'personalInfo.firstName' => 'string|max:255',
            'personalInfo.lastName' => 'string|max:255',
            'personalInfo.email' => 'email|unique:patient_records,personalInfo.email,' . $id,
            'personalInfo.phone' => 'string|max:20',
            'personalInfo.dateOfBirth' => 'date',
            'personalInfo.gender' => 'in:male,female,other',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $updateData = $request->all();
        $updateData['updatedAt'] = now();

        $patient->update($updateData);

        // Log update
        ActivityLog::create([
            'user_id' => auth()->id(),
            'action' => 'update_patient',
            'description' => "Updated patient record: {$patient->personalInfo['firstName']} {$patient->personalInfo['lastName']}",
            'resource_id' => $id,
            'ip_address' => $request->ip(),
            'timestamp' => now(),
        ]);

        return response()->json($patient);
    }

    /**
     * Add visit note to patient record
     */
    public function addVisitNote(Request $request, $id)
    {
        $patient = PatientRecord::find($id);

        if (!$patient) {
            return response()->json(['error' => 'Patient not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'note' => 'required|string',
            'diagnosis' => 'string',
            'treatment' => 'string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $patient->addVisitNote($request->all(), auth()->id());

        // Log visit note
        ActivityLog::create([
            'user_id' => auth()->id(),
            'action' => 'add_visit_note',
            'description' => "Added visit note for patient: {$patient->personalInfo['firstName']} {$patient->personalInfo['lastName']}",
            'resource_id' => $id,
            'ip_address' => $request->ip(),
            'timestamp' => now(),
        ]);

        return response()->json(['message' => 'Visit note added successfully']);
    }

    /**
     * Add prescription to patient record
     */
    public function addPrescription(Request $request, $id)
    {
        $patient = PatientRecord::find($id);

        if (!$patient) {
            return response()->json(['error' => 'Patient not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'medication' => 'required|string',
            'dosage' => 'required|string',
            'frequency' => 'required|string',
            'duration' => 'required|string',
            'instructions' => 'string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $patient->addPrescription($request->all(), auth()->id());

        // Log prescription
        ActivityLog::create([
            'user_id' => auth()->id(),
            'action' => 'add_prescription',
            'description' => "Added prescription for patient: {$patient->personalInfo['firstName']} {$patient->personalInfo['lastName']}",
            'resource_id' => $id,
            'ip_address' => $request->ip(),
            'timestamp' => now(),
        ]);

        return response()->json(['message' => 'Prescription added successfully']);
    }

    /**
     * Upload patient document
     */
    public function uploadDocument(Request $request, $id)
    {
        $patient = PatientRecord::find($id);

        if (!$patient) {
            return response()->json(['error' => 'Patient not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'document' => 'required|file|mimes:pdf,jpg,jpeg,png|max:10240', // 10MB max
            'type' => 'required|string|in:lab_result,prescription,scan,other',
            'description' => 'string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $file = $request->file('document');
        $filename = time() . '_' . $file->getClientOriginalName();
        $path = $file->storeAs('patient-documents/' . $id, $filename, 's3');

        $documents = $patient->documents ?? [];
        $documents[] = [
            'id' => (string) new \MongoDB\BSON\ObjectId(),
            'filename' => $filename,
            'path' => $path,
            'type' => $request->type,
            'description' => $request->description,
            'uploadedBy' => auth()->id(),
            'uploadedAt' => now(),
        ];

        $patient->update(['documents' => $documents]);

        // Log document upload
        ActivityLog::create([
            'user_id' => auth()->id(),
            'action' => 'upload_document',
            'description' => "Uploaded document for patient: {$patient->personalInfo['firstName']} {$patient->personalInfo['lastName']}",
            'resource_id' => $id,
            'ip_address' => $request->ip(),
            'timestamp' => now(),
        ]);

        return response()->json(['message' => 'Document uploaded successfully', 'path' => $path]);
    }
}
