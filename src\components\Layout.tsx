import React from "react"
import { <PERSON>, useNavigate } from "react-router-dom"
import { useAuth } from "../contexts/AuthContext"
import { useRouteData } from "../hooks/useRouteData"
import {
  HomeIcon,
  UserGroupIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  CreditCardIcon,
  ChartBarIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon,
} from "@heroicons/react/24/outline"

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { state, logout } = useAuth()
  const navigate = useNavigate()
  const { isActive, isPartiallyActive } = useRouteData()
  const [sidebarOpen, setSidebarOpen] = React.useState(false)

  const navigation = [
    {
      name: "Dashboard",
      href: "/dashboard",
      icon: HomeIcon,
      roles: ["patient", "doctor", "admin"],
      isActive: isActive("/dashboard"),
    },
    {
      name: "Patient Records",
      href: "/patients",
      icon: User<PERSON>roup<PERSON><PERSON>,
      roles: ["doctor", "admin"],
      isActive: isPartiallyActive("/patients"),
    },
    {
      name: "Appointments",
      href: "/appointments",
      icon: CalendarIcon,
      roles: ["patient", "doctor", "admin"],
      isActive: isPartiallyActive("/appointments"),
    },
    {
      name: "Facilities",
      href: "/facilities",
      icon: BuildingOfficeIcon,
      roles: ["patient", "doctor", "admin"],
      isActive: isPartiallyActive("/facilities"),
    },
    {
      name: "Billing",
      href: "/billing",
      icon: CreditCardIcon,
      roles: ["doctor", "admin"],
      isActive: isPartiallyActive("/billing"),
    },
    {
      name: "Admin Panel",
      href: "/admin",
      icon: ChartBarIcon,
      roles: ["admin"],
      isActive: isActive("/admin"),
    },
  ]

  const filteredNavigation = navigation.filter((item) => item.roles.includes(state.user?.role || "patient"))

  const handleLogout = () => {
    logout()
    navigate("/login", { replace: true })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? "block" : "hidden"}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl">
          <div className="flex h-16 items-center justify-between px-4">
            <h1 className="text-xl font-bold text-blue-600">AfyaSecure</h1>
            <button onClick={() => setSidebarOpen(false)}>
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {filteredNavigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                  item.isActive ? "bg-blue-100 text-blue-900" : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                }`}
                onClick={() => setSidebarOpen(false)}
              >
                <item.icon className="mr-3 h-6 w-6" />
                {item.name}
              </Link>
            ))}
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white border-r border-gray-200">
          <div className="flex h-16 items-center px-4">
            <h1 className="text-xl font-bold text-blue-600">AfyaSecure</h1>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {filteredNavigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                  item.isActive ? "bg-blue-100 text-blue-900" : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                }`}
              >
                <item.icon className="mr-3 h-6 w-6" />
                {item.name}
              </Link>
            ))}
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-16 bg-white shadow">
          <button className="px-4 text-gray-500 focus:outline-none lg:hidden" onClick={() => setSidebarOpen(true)}>
            <Bars3Icon className="h-6 w-6" />
          </button>

          <div className="flex flex-1 justify-end px-4">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <img
                  className="h-8 w-8 rounded-full"
                  src={state.user?.profilePhoto || "/default-avatar.png"}
                  alt="Profile"
                />
                <span className="text-sm font-medium text-gray-700">
                  {state.user?.firstName} {state.user?.lastName}
                </span>
              </div>

              <Link to="/profile" className="text-gray-400 hover:text-gray-500 transition-colors">
                <UserCircleIcon className="h-6 w-6" />
              </Link>

              <button onClick={handleLogout} className="text-gray-400 hover:text-gray-500 transition-colors">
                <ArrowRightOnRectangleIcon className="h-6 w-6" />
              </button>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">{children}</div>
        </main>
      </div>
    </div>
  )
}

export default Layout
