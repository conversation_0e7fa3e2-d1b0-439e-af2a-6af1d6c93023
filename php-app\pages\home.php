<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - Home</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h3 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }
        
        .card p {
            color: #666;
            margin-bottom: 1.5rem;
        }
        
        .btn {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 0.8rem 1.5rem;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .status {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .status h3 {
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><?php echo APP_NAME; ?></h1>
            <p>Modern PHP Health Management System with MongoDB Atlas</p>
        </div>
        
        <div class="cards">
            <div class="card">
                <h3>🏥 Patient Management</h3>
                <p>Manage patient records, medical history, and appointments with secure MongoDB storage.</p>
                <a href="/php-app/api/patients" class="btn">View Patients API</a>
            </div>
            
            <div class="card">
                <h3>👥 User Management</h3>
                <p>Handle user accounts, roles, and permissions for healthcare professionals.</p>
                <a href="/php-app/api/users" class="btn">View Users API</a>
            </div>
            
            <div class="card">
                <h3>📊 Health Analytics</h3>
                <p>Generate reports and analytics from patient data and system usage.</p>
                <a href="#" class="btn">Coming Soon</a>
            </div>
        </div>
        
        <div class="status">
            <h3>System Status</h3>
            <div class="status-item">
                <span>Application</span>
                <span class="status-badge status-success">Running</span>
            </div>
            <div class="status-item">
                <span>Environment</span>
                <span><?php echo APP_ENV; ?></span>
            </div>
            <div class="status-item">
                <span>PHP Version</span>
                <span><?php echo PHP_VERSION; ?></span>
            </div>
            <div class="status-item">
                <span>Database</span>
                <?php
                try {
                    $db = Database::getInstance();
                    echo '<span class="status-badge status-success">Connected</span>';
                } catch (Exception $e) {
                    echo '<span class="status-badge status-error">Disconnected</span>';
                }
                ?>
            </div>
            <div class="status-item">
                <span>Health Check</span>
                <a href="/php-app/api/health" class="btn" style="padding: 0.3rem 0.8rem; font-size: 0.8rem;">Check API</a>
            </div>
        </div>
    </div>
</body>
</html>
