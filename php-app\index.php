<?php
/**
 * AfyaSecure Health Management System - PHP Version
 * Entry point for the application
 */

// Start session
session_start();

// Set error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set timezone
date_default_timezone_set('UTC');

// Include configuration and autoloader
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/autoloader.php';
require_once __DIR__ . '/includes/database.php';

// Simple routing
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);
$path = str_replace('/php-app', '', $path); // Remove base path

// Remove query string
$path = strtok($path, '?');

// Basic routing
switch ($path) {
    case '/':
    case '/home':
        include 'pages/home.php';
        break;
    
    case '/api/health':
        include 'api/health.php';
        break;
    
    case '/api/users':
        include 'api/users.php';
        break;
    
    case '/api/patients':
        include 'api/patients.php';
        break;
    
    default:
        http_response_code(404);
        include 'pages/404.php';
        break;
}
?>
