{"name": "afyasecure-nodejs", "version": "1.0.0", "description": "AfyaSecure Health Management System - Node.js with MongoDB Atlas", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "format": "prettier --write ."}, "keywords": ["nodejs", "mongodb", "health", "management", "api", "express"], "author": "AfyaSecure Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "compression": "^1.7.4", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.56.0", "prettier": "^3.1.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/afyasecure/nodejs-backend.git"}, "bugs": {"url": "https://github.com/afyasecure/nodejs-backend/issues"}, "homepage": "https://github.com/afyasecure/nodejs-backend#readme"}