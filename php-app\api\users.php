<?php
/**
 * Users API endpoint
 */

$method = $_SERVER['REQUEST_METHOD'];
$db = Database::getInstance();

switch ($method) {
    case 'GET':
        handleGetUsers($db);
        break;
    
    case 'POST':
        handleCreateUser($db);
        break;
    
    case 'PUT':
        handleUpdateUser($db);
        break;
    
    case 'DELETE':
        handleDeleteUser($db);
        break;
    
    default:
        sendJsonResponse(['error' => 'Method not allowed'], 405);
        break;
}

function handleGetUsers($db) {
    try {
        // Get query parameters
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
        $skip = isset($_GET['skip']) ? (int)$_GET['skip'] : 0;
        $search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
        
        // Build filter
        $filter = [];
        if (!empty($search)) {
            $filter['$or'] = [
                ['name' => new MongoDB\BSON\Regex($search, 'i')],
                ['email' => new MongoDB\BSON\Regex($search, 'i')]
            ];
        }
        
        // Get users
        $users = $db->find('users', $filter, [
            'limit' => $limit,
            'skip' => $skip,
            'sort' => ['created_at' => -1]
        ]);
        
        $userList = [];
        foreach ($users as $user) {
            // Remove sensitive data
            unset($user['password']);
            $userList[] = $user;
        }
        
        sendJsonResponse([
            'success' => true,
            'data' => $userList,
            'count' => count($userList)
        ]);
        
    } catch (Exception $e) {
        logMessage("Get users error: " . $e->getMessage(), 'error');
        sendJsonResponse(['error' => 'Failed to fetch users'], 500);
    }
}

function handleCreateUser($db) {
    try {
        $input = getJsonInput();
        
        // Validate required fields
        $required = ['name', 'email', 'password', 'role'];
        $missing = validateRequired($input, $required);
        
        if (!empty($missing)) {
            sendJsonResponse([
                'error' => 'Missing required fields',
                'missing_fields' => $missing
            ], 400);
        }
        
        // Sanitize input
        $userData = sanitizeInput($input);
        
        // Validate email
        if (!filter_var($userData['email'], FILTER_VALIDATE_EMAIL)) {
            sendJsonResponse(['error' => 'Invalid email format'], 400);
        }
        
        // Check if user already exists
        $existingUser = $db->findOne('users', ['email' => $userData['email']]);
        if ($existingUser) {
            sendJsonResponse(['error' => 'User with this email already exists'], 409);
        }
        
        // Hash password
        $userData['password'] = hashPassword($userData['password']);
        
        // Add metadata
        $userData['id'] = generateUUID();
        $userData['created_at'] = new MongoDB\BSON\UTCDateTime();
        $userData['updated_at'] = new MongoDB\BSON\UTCDateTime();
        $userData['status'] = 'active';
        
        // Insert user
        $result = $db->insertOne('users', $userData);
        
        if ($result) {
            // Remove password from response
            unset($userData['password']);
            
            sendJsonResponse([
                'success' => true,
                'message' => 'User created successfully',
                'data' => $userData
            ], 201);
        } else {
            sendJsonResponse(['error' => 'Failed to create user'], 500);
        }
        
    } catch (Exception $e) {
        logMessage("Create user error: " . $e->getMessage(), 'error');
        sendJsonResponse(['error' => 'Failed to create user'], 500);
    }
}

function handleUpdateUser($db) {
    try {
        $input = getJsonInput();
        
        if (!isset($input['id'])) {
            sendJsonResponse(['error' => 'User ID is required'], 400);
        }
        
        $userId = sanitizeInput($input['id']);
        unset($input['id']);
        
        // Sanitize input
        $updateData = sanitizeInput($input);
        
        // Hash password if provided
        if (isset($updateData['password'])) {
            $updateData['password'] = hashPassword($updateData['password']);
        }
        
        // Add updated timestamp
        $updateData['updated_at'] = new MongoDB\BSON\UTCDateTime();
        
        // Update user
        $result = $db->updateOne('users', 
            ['id' => $userId], 
            ['$set' => $updateData]
        );
        
        if ($result->getModifiedCount() > 0) {
            sendJsonResponse([
                'success' => true,
                'message' => 'User updated successfully'
            ]);
        } else {
            sendJsonResponse(['error' => 'User not found or no changes made'], 404);
        }
        
    } catch (Exception $e) {
        logMessage("Update user error: " . $e->getMessage(), 'error');
        sendJsonResponse(['error' => 'Failed to update user'], 500);
    }
}

function handleDeleteUser($db) {
    try {
        $input = getJsonInput();
        
        if (!isset($input['id'])) {
            sendJsonResponse(['error' => 'User ID is required'], 400);
        }
        
        $userId = sanitizeInput($input['id']);
        
        // Delete user
        $result = $db->deleteOne('users', ['id' => $userId]);
        
        if ($result->getDeletedCount() > 0) {
            sendJsonResponse([
                'success' => true,
                'message' => 'User deleted successfully'
            ]);
        } else {
            sendJsonResponse(['error' => 'User not found'], 404);
        }
        
    } catch (Exception $e) {
        logMessage("Delete user error: " . $e->getMessage(), 'error');
        sendJsonResponse(['error' => 'Failed to delete user'], 500);
    }
}
?>
