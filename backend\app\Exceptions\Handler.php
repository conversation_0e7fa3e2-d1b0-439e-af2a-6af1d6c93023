<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Request;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Render an exception into an HTTP response.
     */
    public function render($request, Throwable $e)
    {
        // Handle API requests
        if ($request->expectsJson()) {
            return $this->handleApiException($request, $e);
        }

        return parent::render($request, $e);
    }

    /**
     * Handle API exceptions
     */
    protected function handleApiException(Request $request, Throwable $e)
    {
        if ($e instanceof AuthenticationException) {
            return response()->json([
                'error' => 'Unauthenticated',
                'message' => 'Authentication required'
            ], 401);
        }

        if ($e instanceof ValidationException) {
            return response()->json([
                'error' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        }

        if ($e instanceof NotFoundHttpException) {
            return response()->json([
                'error' => 'Not found',
                'message' => 'The requested resource was not found'
            ], 404);
        }

        if ($e instanceof MethodNotAllowedHttpException) {
            return response()->json([
                'error' => 'Method not allowed',
                'message' => 'The HTTP method is not allowed for this endpoint'
            ], 405);
        }

        // Handle JWT exceptions
        if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenExpiredException) {
            return response()->json([
                'error' => 'Token expired',
                'message' => 'Your session has expired. Please login again.'
            ], 401);
        }

        if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenInvalidException) {
            return response()->json([
                'error' => 'Token invalid',
                'message' => 'Invalid authentication token'
            ], 401);
        }

        if ($e instanceof \Tymon\JWTAuth\Exceptions\JWTException) {
            return response()->json([
                'error' => 'Token error',
                'message' => 'Authentication token error'
            ], 401);
        }

        // Handle database exceptions
        if ($e instanceof \MongoDB\Driver\Exception\Exception) {
            return response()->json([
                'error' => 'Database error',
                'message' => config('app.debug') ? $e->getMessage() : 'A database error occurred'
            ], 500);
        }

        // Generic error handling
        $statusCode = method_exists($e, 'getStatusCode') ? $e->getStatusCode() : 500;
        
        return response()->json([
            'error' => 'Server error',
            'message' => config('app.debug') ? $e->getMessage() : 'An unexpected error occurred',
            'trace' => config('app.debug') ? $e->getTraceAsString() : null
        ], $statusCode);
    }

    /**
     * Convert an authentication exception into a response.
     */
    protected function unauthenticated($request, AuthenticationException $exception)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Unauthenticated',
                'message' => 'Authentication required'
            ], 401);
        }

        return redirect()->guest(route('login'));
    }
}
