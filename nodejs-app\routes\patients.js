import express from 'express';
import { body, query, validationResult } from 'express-validator';
import Patient from '../models/Patient.js';
import { authenticate, authorize } from '../middleware/auth.js';
import logger from '../config/logger.js';

const router = express.Router();

// Get all patients
router.get('/', authenticate, authorize('admin', 'doctor', 'nurse', 'receptionist'), [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('status').optional().isIn(['active', 'inactive', 'deceased']).withMessage('Invalid status'),
    query('search').optional().isLength({ min: 1 }).withMessage('Search query cannot be empty')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        const { status, search } = req.query;

        // Build filter
        const filter = {};
        if (status) filter.status = status;

        let query = Patient.find(filter);

        // Add search if provided
        if (search) {
            query = Patient.search(search, { status, limit: limit * 10 }); // Increase limit for search
        }

        // Get patients with pagination
        const [patients, total] = await Promise.all([
            query
                .populate('registeredBy', 'name email')
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit),
            search ? 
                Patient.search(search, { status }).countDocuments() :
                Patient.countDocuments(filter)
        ]);

        const totalPages = Math.ceil(total / limit);

        res.json({
            success: true,
            data: {
                patients,
                pagination: {
                    currentPage: page,
                    totalPages,
                    totalPatients: total,
                    hasNextPage: page < totalPages,
                    hasPrevPage: page > 1
                }
            }
        });
    } catch (error) {
        logger.error('Get patients error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch patients'
        });
    }
});

// Get patient by ID
router.get('/:id', authenticate, authorize('admin', 'doctor', 'nurse', 'receptionist'), async (req, res) => {
    try {
        const patient = await Patient.findById(req.params.id)
            .populate('registeredBy', 'name email')
            .populate('medicalHistory.addedBy', 'name email')
            .populate('allergies.addedBy', 'name email')
            .populate('medications.prescribedBy', 'name email');

        if (!patient) {
            return res.status(404).json({
                success: false,
                message: 'Patient not found'
            });
        }

        res.json({
            success: true,
            data: { patient }
        });
    } catch (error) {
        logger.error('Get patient error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch patient'
        });
    }
});

// Create new patient
router.post('/', authenticate, authorize('admin', 'doctor', 'nurse', 'receptionist'), [
    body('firstName')
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('First name is required and must be less than 50 characters'),
    body('lastName')
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Last name is required and must be less than 50 characters'),
    body('dateOfBirth')
        .isISO8601()
        .withMessage('Please provide a valid date of birth')
        .custom(value => {
            if (new Date(value) >= new Date()) {
                throw new Error('Date of birth must be in the past');
            }
            return true;
        }),
    body('gender')
        .isIn(['male', 'female', 'other'])
        .withMessage('Gender must be male, female, or other'),
    body('phone')
        .matches(/^\+?[\d\s\-\(\)]+$/)
        .withMessage('Please provide a valid phone number'),
    body('email')
        .optional()
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const patientData = {
            ...req.body,
            registeredBy: req.user._id
        };

        const patient = new Patient(patientData);
        await patient.save();

        // Populate the registered by field
        await patient.populate('registeredBy', 'name email');

        logger.logDatabase('patient_created', 'patients', {
            patientId: patient._id,
            patientNumber: patient.patientNumber,
            createdBy: req.user._id,
            ip: req.ip
        });

        res.status(201).json({
            success: true,
            message: 'Patient registered successfully',
            data: { patient }
        });
    } catch (error) {
        logger.error('Create patient error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to register patient'
        });
    }
});

// Update patient
router.put('/:id', authenticate, authorize('admin', 'doctor', 'nurse', 'receptionist'), [
    body('firstName')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('First name must be less than 50 characters'),
    body('lastName')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Last name must be less than 50 characters'),
    body('email')
        .optional()
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address'),
    body('phone')
        .optional()
        .matches(/^\+?[\d\s\-\(\)]+$/)
        .withMessage('Please provide a valid phone number'),
    body('status')
        .optional()
        .isIn(['active', 'inactive', 'deceased'])
        .withMessage('Invalid status')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const allowedUpdates = [
            'firstName', 'lastName', 'middleName', 'email', 'phone', 'alternatePhone',
            'address', 'maritalStatus', 'occupation', 'bloodType', 'insurance',
            'status', 'notes'
        ];

        const updates = {};
        Object.keys(req.body).forEach(key => {
            if (allowedUpdates.includes(key)) {
                updates[key] = req.body[key];
            }
        });

        const patient = await Patient.findByIdAndUpdate(
            req.params.id,
            updates,
            { new: true, runValidators: true }
        ).populate('registeredBy', 'name email');

        if (!patient) {
            return res.status(404).json({
                success: false,
                message: 'Patient not found'
            });
        }

        logger.logDatabase('patient_updated', 'patients', {
            patientId: patient._id,
            patientNumber: patient.patientNumber,
            updatedBy: req.user._id,
            updatedFields: Object.keys(updates),
            ip: req.ip
        });

        res.json({
            success: true,
            message: 'Patient updated successfully',
            data: { patient }
        });
    } catch (error) {
        logger.error('Update patient error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update patient'
        });
    }
});

// Add medical history entry
router.post('/:id/medical-history', authenticate, authorize('admin', 'doctor', 'nurse'), [
    body('condition')
        .trim()
        .isLength({ min: 1 })
        .withMessage('Condition is required'),
    body('diagnosedDate')
        .isISO8601()
        .withMessage('Please provide a valid diagnosed date'),
    body('status')
        .optional()
        .isIn(['active', 'resolved', 'chronic'])
        .withMessage('Invalid status')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const patient = await Patient.findById(req.params.id);
        if (!patient) {
            return res.status(404).json({
                success: false,
                message: 'Patient not found'
            });
        }

        const medicalHistoryEntry = {
            ...req.body,
            addedBy: req.user._id,
            addedAt: new Date()
        };

        patient.medicalHistory.push(medicalHistoryEntry);
        await patient.save();

        await patient.populate('medicalHistory.addedBy', 'name email');

        logger.logDatabase('medical_history_added', 'patients', {
            patientId: patient._id,
            condition: req.body.condition,
            addedBy: req.user._id,
            ip: req.ip
        });

        res.status(201).json({
            success: true,
            message: 'Medical history entry added successfully',
            data: { 
                medicalHistory: patient.medicalHistory[patient.medicalHistory.length - 1]
            }
        });
    } catch (error) {
        logger.error('Add medical history error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to add medical history entry'
        });
    }
});

// Get patient statistics
router.get('/stats/overview', authenticate, authorize('admin', 'doctor'), async (req, res) => {
    try {
        const totalPatients = await Patient.countDocuments();
        const activePatients = await Patient.countDocuments({ status: 'active' });
        const recentPatients = await Patient.countDocuments({
            createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        });

        const genderStats = await Patient.aggregate([
            { $match: { status: 'active' } },
            {
                $group: {
                    _id: '$gender',
                    count: { $sum: 1 }
                }
            }
        ]);

        const ageStats = await Patient.aggregate([
            { $match: { status: 'active' } },
            {
                $addFields: {
                    age: {
                        $floor: {
                            $divide: [
                                { $subtract: [new Date(), '$dateOfBirth'] },
                                365.25 * 24 * 60 * 60 * 1000
                            ]
                        }
                    }
                }
            },
            {
                $group: {
                    _id: {
                        $switch: {
                            branches: [
                                { case: { $lt: ['$age', 18] }, then: '0-17' },
                                { case: { $lt: ['$age', 35] }, then: '18-34' },
                                { case: { $lt: ['$age', 55] }, then: '35-54' },
                                { case: { $lt: ['$age', 75] }, then: '55-74' }
                            ],
                            default: '75+'
                        }
                    },
                    count: { $sum: 1 }
                }
            }
        ]);

        res.json({
            success: true,
            data: {
                totalPatients,
                activePatients,
                recentPatients,
                genderDistribution: genderStats,
                ageDistribution: ageStats
            }
        });
    } catch (error) {
        logger.error('Get patient stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch patient statistics'
        });
    }
});

export default router;
