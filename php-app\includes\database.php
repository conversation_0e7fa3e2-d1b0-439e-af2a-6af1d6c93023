<?php
/**
 * MongoDB Database Connection and Operations
 */

class Database {
    private static $instance = null;
    private $client;
    private $database;
    
    private function __construct() {
        $this->connect();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function connect() {
        try {
            // Check if MongoDB extension is loaded
            if (!extension_loaded('mongodb')) {
                throw new Exception('MongoDB PHP extension is not installed. Please install it first.');
            }
            
            $uri = MONGODB_URI;
            if (empty($uri)) {
                throw new Exception('MongoDB URI is not configured. Please set MONGODB_URI in .env file.');
            }
            
            // Create MongoDB client
            $this->client = new MongoDB\Client($uri);
            
            // Select database
            $this->database = $this->client->selectDatabase(MONGODB_DATABASE);
            
            // Test connection
            $this->database->command(['ping' => 1]);
            
            if (APP_DEBUG) {
                error_log("MongoDB connected successfully to database: " . MONGODB_DATABASE);
            }
            
        } catch (Exception $e) {
            error_log("MongoDB connection failed: " . $e->getMessage());
            
            if (APP_DEBUG) {
                die("Database connection failed: " . $e->getMessage());
            } else {
                die("Database connection failed. Please try again later.");
            }
        }
    }
    
    public function getDatabase() {
        return $this->database;
    }
    
    public function getCollection($collectionName) {
        return $this->database->selectCollection($collectionName);
    }
    
    // Helper method to insert document
    public function insertOne($collection, $document) {
        try {
            $result = $this->getCollection($collection)->insertOne($document);
            return $result->getInsertedId();
        } catch (Exception $e) {
            error_log("Insert failed: " . $e->getMessage());
            return false;
        }
    }
    
    // Helper method to find documents
    public function find($collection, $filter = [], $options = []) {
        try {
            return $this->getCollection($collection)->find($filter, $options);
        } catch (Exception $e) {
            error_log("Find failed: " . $e->getMessage());
            return false;
        }
    }
    
    // Helper method to find one document
    public function findOne($collection, $filter = [], $options = []) {
        try {
            return $this->getCollection($collection)->findOne($filter, $options);
        } catch (Exception $e) {
            error_log("FindOne failed: " . $e->getMessage());
            return false;
        }
    }
    
    // Helper method to update document
    public function updateOne($collection, $filter, $update, $options = []) {
        try {
            return $this->getCollection($collection)->updateOne($filter, $update, $options);
        } catch (Exception $e) {
            error_log("Update failed: " . $e->getMessage());
            return false;
        }
    }
    
    // Helper method to delete document
    public function deleteOne($collection, $filter) {
        try {
            return $this->getCollection($collection)->deleteOne($filter);
        } catch (Exception $e) {
            error_log("Delete failed: " . $e->getMessage());
            return false;
        }
    }
}

// Initialize database connection
try {
    $db = Database::getInstance();
} catch (Exception $e) {
    error_log("Failed to initialize database: " . $e->getMessage());
    if (APP_DEBUG) {
        echo "Database initialization failed: " . $e->getMessage();
    }
}
?>
