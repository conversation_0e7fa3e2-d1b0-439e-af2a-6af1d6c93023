<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\PatientRecord;
use App\Models\Appointment;
use App\Models\Billing;
use App\Models\ActivityLog;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * Get dashboard statistics based on user role
     */
    public function getStats(Request $request)
    {
        $user = auth()->user();
        
        switch ($user->role) {
            case 'admin':
                return $this->getAdminStats();
            case 'doctor':
                return $this->getDoctorStats($user);
            case 'patient':
                return $this->getPatientStats($user);
            default:
                return response()->json(['error' => 'Invalid user role'], 400);
        }
    }

    /**
     * Get admin dashboard statistics
     */
    private function getAdminStats()
    {
        $totalUsers = User::count();
        $totalPatients = User::where('role', 'patient')->count();
        $totalDoctors = User::where('role', 'doctor')->count();
        $totalAppointments = Appointment::count();
        $upcomingAppointments = Appointment::where('date', '>=', now())
            ->where('status', 'scheduled')
            ->count();
        $totalRevenue = Billing::where('status', 'paid')->sum('total');
        $pendingBills = Billing::where('status', 'pending')->count();

        // Recent activity
        $recentActivity = ActivityLog::orderBy('timestamp', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($log) {
                return [
                    'id' => $log->_id,
                    'type' => $log->action,
                    'description' => $log->description,
                    'timestamp' => $log->timestamp,
                ];
            });

        return response()->json([
            'totalUsers' => $totalUsers,
            'totalPatients' => $totalPatients,
            'totalDoctors' => $totalDoctors,
            'totalAppointments' => $totalAppointments,
            'upcomingAppointments' => $upcomingAppointments,
            'totalRevenue' => $totalRevenue,
            'pendingBills' => $pendingBills,
            'recentActivity' => $recentActivity,
        ]);
    }

    /**
     * Get doctor dashboard statistics
     */
    private function getDoctorStats($user)
    {
        $totalPatients = PatientRecord::where('doctorId', $user->_id)->count();
        $totalAppointments = Appointment::where('doctorId', $user->_id)->count();
        $upcomingAppointments = Appointment::where('doctorId', $user->_id)
            ->where('date', '>=', now())
            ->where('status', 'scheduled')
            ->count();
        $pendingBills = Billing::where('doctorId', $user->_id)
            ->where('status', 'pending')
            ->count();

        // Today's appointments
        $todayAppointments = Appointment::where('doctorId', $user->_id)
            ->whereDate('date', today())
            ->orderBy('time')
            ->get()
            ->map(function ($appointment) {
                return [
                    'id' => $appointment->_id,
                    'patientName' => $appointment->patientName,
                    'time' => $appointment->time,
                    'type' => $appointment->type,
                    'status' => $appointment->status,
                ];
            });

        // Recent activity
        $recentActivity = ActivityLog::where('user_id', $user->_id)
            ->orderBy('timestamp', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($log) {
                return [
                    'id' => $log->_id,
                    'type' => $log->action,
                    'description' => $log->description,
                    'timestamp' => $log->timestamp,
                ];
            });

        return response()->json([
            'totalPatients' => $totalPatients,
            'totalAppointments' => $totalAppointments,
            'upcomingAppointments' => $upcomingAppointments,
            'pendingBills' => $pendingBills,
            'todayAppointments' => $todayAppointments,
            'recentActivity' => $recentActivity,
        ]);
    }

    /**
     * Get patient dashboard statistics
     */
    private function getPatientStats($user)
    {
        $totalAppointments = Appointment::where('patientId', $user->_id)->count();
        $upcomingAppointments = Appointment::where('patientId', $user->_id)
            ->where('date', '>=', now())
            ->where('status', 'scheduled')
            ->count();
        $completedAppointments = Appointment::where('patientId', $user->_id)
            ->where('status', 'completed')
            ->count();
        $pendingBills = Billing::where('patientId', $user->_id)
            ->whereIn('status', ['pending', 'overdue'])
            ->count();

        // Next appointment
        $nextAppointment = Appointment::where('patientId', $user->_id)
            ->where('date', '>=', now())
            ->where('status', 'scheduled')
            ->orderBy('date')
            ->orderBy('time')
            ->first();

        // Recent medical records
        $recentRecords = PatientRecord::where('patientId', $user->_id)
            ->orderBy('updatedAt', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($record) {
                return [
                    'id' => $record->_id,
                    'doctorName' => $record->doctorName ?? 'Unknown',
                    'lastVisit' => $record->updatedAt,
                    'notes' => count($record->visitNotes ?? []),
                ];
            });

        // Recent activity
        $recentActivity = ActivityLog::where('user_id', $user->_id)
            ->orderBy('timestamp', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($log) {
                return [
                    'id' => $log->_id,
                    'type' => $log->action,
                    'description' => $log->description,
                    'timestamp' => $log->timestamp,
                ];
            });

        return response()->json([
            'totalAppointments' => $totalAppointments,
            'upcomingAppointments' => $upcomingAppointments,
            'completedAppointments' => $completedAppointments,
            'pendingBills' => $pendingBills,
            'nextAppointment' => $nextAppointment,
            'recentRecords' => $recentRecords,
            'recentActivity' => $recentActivity,
        ]);
    }
}
