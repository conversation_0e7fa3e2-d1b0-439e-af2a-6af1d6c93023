<?php

namespace App\Models;

use Illuminate\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use <PERSON><PERSON>\Lumen\Auth\Authorizable;
use <PERSON><PERSON>gers\Mongodb\Eloquent\Model;
use Tymon\JWTAuth\Contracts\JWTSubject;

class User extends Model implements AuthenticatableContract, AuthorizableContract, JWTSubject
{
    use Authenticatable, Authorizable, HasFactory;

    protected $connection = 'mongodb';
    protected $collection = 'users';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'firstName',
        'lastName',
        'email',
        'password',
        'role',
        'phone',
        'dateOfBirth',
        'profilePhoto',
        'verified',
        'qrCode',
        'refreshToken',
        'refreshTokenExpiry',
        'medicalLicense', // for doctors
        'specialization', // for doctors
        'facilityId', // for doctors
        'emergencyContact',
        'address',
        'createdAt',
        'updatedAt',
    ];

    /**
     * The attributes excluded from the model's JSON form.
     */
    protected $hidden = [
        'password',
        'refreshToken',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'verified' => 'boolean',
        'dateOfBirth' => 'date',
        'refreshTokenExpiry' => 'datetime',
        'createdAt' => 'datetime',
        'updatedAt' => 'datetime',
    ];

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     */
    public function getJWTCustomClaims()
    {
        return [
            'role' => $this->role,
            'verified' => $this->verified,
        ];
    }

    /**
     * Get user's full name
     */
    public function getFullNameAttribute()
    {
        return $this->firstName . ' ' . $this->lastName;
    }

    /**
     * Check if user has specific role
     */
    public function hasRole($role)
    {
        return $this->role === $role;
    }

    /**
     * Check if user can access resource
     */
    public function canAccess($resource, $action = 'read')
    {
        $permissions = [
            'admin' => ['*'],
            'doctor' => ['patients', 'appointments', 'billing', 'facilities'],
            'patient' => ['appointments', 'facilities', 'profile'],
        ];

        return in_array('*', $permissions[$this->role] ?? []) || 
               in_array($resource, $permissions[$this->role] ?? []);
    }

    /**
     * Get user's patients (for doctors)
     */
    public function patients()
    {
        if ($this->role !== 'doctor') {
            return collect();
        }

        return PatientRecord::where('doctorId', $this->_id)->get();
    }

    /**
     * Get user's appointments
     */
    public function appointments()
    {
        $field = $this->role === 'patient' ? 'patientId' : 'doctorId';
        return Appointment::where($field, $this->_id)->get();
    }
}
