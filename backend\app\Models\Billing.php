<?php

namespace App\Models;

use Jenssegers\Mongodb\Eloquent\Model;
use Carbon\Carbon;

class Billing extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'billing';

    protected $fillable = [
        'patientId',
        'doctorId',
        'facilityId',
        'appointmentId',
        'patientName',
        'doctorName',
        'facilityName',
        'items',
        'subtotal',
        'tax',
        'discount',
        'total',
        'status',
        'paymentMethod',
        'paymentReference',
        'insuranceClaim',
        'notes',
        'createdDate',
        'dueDate',
        'paidDate',
        'approvedBy',
        'approvedDate',
        'invoiceNumber',
        'createdAt',
        'updatedAt',
    ];

    protected $casts = [
        'items' => 'array',
        'subtotal' => 'float',
        'tax' => 'float',
        'discount' => 'float',
        'total' => 'float',
        'insuranceClaim' => 'array',
        'createdDate' => 'date',
        'dueDate' => 'date',
        'paidDate' => 'date',
        'approvedDate' => 'date',
        'createdAt' => 'datetime',
        'updatedAt' => 'datetime',
    ];

    /**
     * Get the patient associated with the bill
     */
    public function patient()
    {
        return $this->belongsTo(User::class, 'patientId', '_id');
    }

    /**
     * Get the doctor associated with the bill
     */
    public function doctor()
    {
        return $this->belongsTo(User::class, 'doctorId', '_id');
    }

    /**
     * Get the facility associated with the bill
     */
    public function facility()
    {
        return $this->belongsTo(Facility::class, 'facilityId', '_id');
    }

    /**
     * Get the appointment associated with the bill
     */
    public function appointment()
    {
        return $this->belongsTo(Appointment::class, 'appointmentId', '_id');
    }

    /**
     * Scope for bills by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for overdue bills
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'pending')
                    ->where('dueDate', '<', now());
    }

    /**
     * Scope for bills within date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('createdDate', [$startDate, $endDate]);
    }

    /**
     * Check if bill is overdue
     */
    public function getIsOverdueAttribute()
    {
        return $this->status === 'pending' && 
               $this->dueDate && 
               Carbon::parse($this->dueDate)->isPast();
    }

    /**
     * Get days until due date
     */
    public function getDaysUntilDueAttribute()
    {
        if (!$this->dueDate) return null;
        
        return now()->diffInDays(Carbon::parse($this->dueDate), false);
    }

    /**
     * Generate invoice number
     */
    public function generateInvoiceNumber()
    {
        $prefix = 'INV';
        $year = now()->year;
        $month = now()->format('m');
        
        // Get the last invoice number for this month
        $lastInvoice = self::where('invoiceNumber', 'regex', "/^{$prefix}-{$year}{$month}/")
                          ->orderBy('invoiceNumber', 'desc')
                          ->first();

        if ($lastInvoice) {
            $lastNumber = (int) substr($lastInvoice->invoiceNumber, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        $invoiceNumber = sprintf('%s-%s%s-%04d', $prefix, $year, $month, $newNumber);
        
        $this->update(['invoiceNumber' => $invoiceNumber]);
        
        return $invoiceNumber;
    }

    /**
     * Add item to bill
     */
    public function addItem($description, $quantity, $unitPrice)
    {
        $items = $this->items ?? [];
        
        $item = [
            'id' => (string) new \MongoDB\BSON\ObjectId(),
            'description' => $description,
            'quantity' => $quantity,
            'unitPrice' => $unitPrice,
            'total' => $quantity * $unitPrice,
        ];

        $items[] = $item;
        
        $this->update(['items' => $items]);
        $this->calculateTotals();
        
        return $item;
    }

    /**
     * Remove item from bill
     */
    public function removeItem($itemId)
    {
        $items = $this->items ?? [];
        
        $items = array_filter($items, function ($item) use ($itemId) {
            return $item['id'] !== $itemId;
        });

        $this->update(['items' => array_values($items)]);
        $this->calculateTotals();
        
        return $this;
    }

    /**
     * Calculate bill totals
     */
    public function calculateTotals()
    {
        $items = $this->items ?? [];
        $subtotal = array_sum(array_column($items, 'total'));
        
        $taxRate = 0.08; // 8% tax rate
        $tax = $subtotal * $taxRate;
        
        $discount = $this->discount ?? 0;
        $total = $subtotal + $tax - $discount;

        $this->update([
            'subtotal' => round($subtotal, 2),
            'tax' => round($tax, 2),
            'total' => round($total, 2),
        ]);

        return $this;
    }

    /**
     * Approve bill
     */
    public function approve($approvedBy = null)
    {
        $this->update([
            'status' => 'approved',
            'approvedBy' => $approvedBy ?? auth()->id(),
            'approvedDate' => now(),
        ]);

        // Generate invoice number if not exists
        if (!$this->invoiceNumber) {
            $this->generateInvoiceNumber();
        }

        // Log approval
        ActivityLog::create([
            'user_id' => auth()->id(),
            'action' => 'bill_approved',
            'description' => "Bill #{$this->_id} approved",
            'metadata' => [
                'bill_id' => $this->_id,
                'patient_id' => $this->patientId,
                'total_amount' => $this->total,
            ],
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now(),
        ]);

        return $this;
    }

    /**
     * Mark bill as paid
     */
    public function markAsPaid($paymentMethod = null, $paymentReference = null)
    {
        $this->update([
            'status' => 'paid',
            'paidDate' => now(),
            'paymentMethod' => $paymentMethod,
            'paymentReference' => $paymentReference,
        ]);

        // Log payment
        ActivityLog::create([
            'user_id' => auth()->id(),
            'action' => 'bill_paid',
            'description' => "Bill #{$this->_id} marked as paid",
            'metadata' => [
                'bill_id' => $this->_id,
                'patient_id' => $this->patientId,
                'amount_paid' => $this->total,
                'payment_method' => $paymentMethod,
            ],
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now(),
        ]);

        return $this;
    }

    /**
     * Generate invoice data for PDF/printing
     */
    public function generateInvoiceData()
    {
        return [
            'invoice_number' => $this->invoiceNumber,
            'bill_id' => $this->_id,
            'created_date' => $this->createdDate,
            'due_date' => $this->dueDate,
            'status' => $this->status,
            'patient' => [
                'name' => $this->patientName,
                'id' => $this->patientId,
            ],
            'doctor' => [
                'name' => $this->doctorName,
                'id' => $this->doctorId,
            ],
            'facility' => [
                'name' => $this->facilityName,
                'id' => $this->facilityId,
            ],
            'items' => $this->items,
            'subtotal' => $this->subtotal,
            'tax' => $this->tax,
            'discount' => $this->discount,
            'total' => $this->total,
            'notes' => $this->notes,
            'payment_info' => [
                'method' => $this->paymentMethod,
                'reference' => $this->paymentReference,
                'paid_date' => $this->paidDate,
            ],
            'approval_info' => [
                'approved_by' => $this->approvedBy,
                'approved_date' => $this->approvedDate,
            ],
        ];
    }

    /**
     * Get bill statistics for a period
     */
    public static function getStats($startDate = null, $endDate = null)
    {
        $query = self::query();
        
        if ($startDate && $endDate) {
            $query->whereBetween('createdDate', [$startDate, $endDate]);
        }

        $totalBills = $query->count();
        $totalRevenue = $query->where('status', 'paid')->sum('total');
        $pendingAmount = $query->where('status', 'pending')->sum('total');
        $overdueAmount = $query->where('status', 'pending')
                              ->where('dueDate', '<', now())
                              ->sum('total');

        return [
            'total_bills' => $totalBills,
            'total_revenue' => $totalRevenue,
            'pending_amount' => $pendingAmount,
            'overdue_amount' => $overdueAmount,
            'collection_rate' => $totalBills > 0 ? ($totalRevenue / ($totalRevenue + $pendingAmount)) * 100 : 0,
        ];
    }
}
