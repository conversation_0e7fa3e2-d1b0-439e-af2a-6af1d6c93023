<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        //
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Define Gates for role-based access
        Gate::define('admin-access', function ($user) {
            return $user->role === 'admin';
        });

        Gate::define('doctor-access', function ($user) {
            return in_array($user->role, ['doctor', 'admin']);
        });

        Gate::define('patient-access', function ($user) {
            return in_array($user->role, ['patient', 'doctor', 'admin']);
        });

        Gate::define('manage-users', function ($user) {
            return $user->role === 'admin';
        });

        Gate::define('manage-facilities', function ($user) {
            return $user->role === 'admin';
        });

        Gate::define('approve-bills', function ($user) {
            return $user->role === 'admin';
        });
    }
}
