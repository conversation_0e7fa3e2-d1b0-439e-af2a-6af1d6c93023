<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Clean old activity logs daily at 2 AM
        $schedule->call(function () {
            \App\Models\ActivityLog::cleanOldLogs(90);
        })->daily()->at('02:00');

        // Send appointment reminders
        $schedule->call(function () {
            // Implementation for sending appointment reminders
            // This would check for appointments in the next 24 hours
            // and send notifications to patients
        })->hourly();

        // Check for overdue bills
        $schedule->call(function () {
            // Implementation for checking overdue bills
            // and updating their status
        })->daily()->at('06:00');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
