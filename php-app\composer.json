{"name": "afyasecure/php-app", "description": "AfyaSecure Health Management System - Pure PHP with MongoDB Atlas", "type": "project", "keywords": ["php", "mongodb", "health", "management", "api"], "license": "MIT", "authors": [{"name": "AfyaSecure Team", "email": "<EMAIL>"}], "require": {"php": "^8.2", "ext-mongodb": "*", "ext-json": "*", "ext-mbstring": "*", "mongodb/mongodb": "^1.15"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "autoload": {"psr-4": {"AfyaSecure\\": "classes/"}, "files": ["includes/functions.php"]}, "autoload-dev": {"psr-4": {"AfyaSecure\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "serve": "php -S localhost:8000 -t .", "check-requirements": ["@php -r \"echo 'PHP Version: ' . PHP_VERSION . PHP_EOL;\"", "@php -r \"echo 'MongoDB Extension: ' . (extension_loaded('mongodb') ? 'Installed' : 'NOT INSTALLED') . PHP_EOL;\"", "@php -r \"echo 'JSON Extension: ' . (extension_loaded('json') ? 'Installed' : 'NOT INSTALLED') . PHP_EOL;\"", "@php -r \"echo 'MBString Extension: ' . (extension_loaded('mbstring') ? 'Installed' : 'NOT INSTALLED') . PHP_EOL;\""]}, "config": {"optimize-autoloader": true, "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}