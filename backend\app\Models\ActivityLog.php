<?php

namespace App\Models;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model;

class ActivityLog extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'activity_logs';

    protected $fillable = [
        'user_id',
        'action',
        'description',
        'metadata',
        'ip_address',
        'user_agent',
        'timestamp',
    ];

    protected $casts = [
        'metadata' => 'array',
        'timestamp' => 'datetime',
    ];

    /**
     * Disable Laravel's automatic timestamps since we use custom timestamp
     */
    public $timestamps = false;

    /**
     * Get the user associated with the activity log
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', '_id');
    }

    /**
     * Scope for logs by user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope for logs by action
     */
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope for logs within date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('timestamp', [$startDate, $endDate]);
    }

    /**
     * Scope for recent logs
     */
    public function scopeRecent($query, $limit = 50)
    {
        return $query->orderBy('timestamp', 'desc')->limit($limit);
    }

    /**
     * Get user name for the log entry
     */
    public function getUserNameAttribute()
    {
        if ($this->user) {
            return $this->user->firstName . ' ' . $this->user->lastName;
        }
        return 'Unknown User';
    }

    /**
     * Log user activity
     */
    public static function logActivity($action, $description, $metadata = [], $userId = null)
    {
        return self::create([
            'user_id' => $userId ?? auth()->id(),
            'action' => $action,
            'description' => $description,
            'metadata' => $metadata,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now(),
        ]);
    }

    /**
     * Get activity statistics
     */
    public static function getStats($startDate = null, $endDate = null)
    {
        $query = self::query();
        
        if ($startDate && $endDate) {
            $query->whereBetween('timestamp', [$startDate, $endDate]);
        }

        $totalActivities = $query->count();
        $uniqueUsers = $query->distinct('user_id')->count();
        
        $topActions = $query->groupBy('action')
                           ->selectRaw('action, count(*) as count')
                           ->orderBy('count', 'desc')
                           ->limit(10)
                           ->get();

        $dailyActivity = $query->groupBy(function ($item) {
                                return $item->timestamp->format('Y-m-d');
                            })
                            ->map(function ($items) {
                                return $items->count();
                            });

        return [
            'total_activities' => $totalActivities,
            'unique_users' => $uniqueUsers,
            'top_actions' => $topActions,
            'daily_activity' => $dailyActivity,
        ];
    }

    /**
     * Clean old logs (for maintenance)
     */
    public static function cleanOldLogs($daysToKeep = 90)
    {
        $cutoffDate = now()->subDays($daysToKeep);
        
        return self::where('timestamp', '<', $cutoffDate)->delete();
    }
}
