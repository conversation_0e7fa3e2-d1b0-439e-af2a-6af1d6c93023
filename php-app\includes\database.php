<?php
/**
 * MongoDB Atlas Database Connection and Operations
 */

// Include MongoDB library if available
if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
    require_once __DIR__ . '/../vendor/autoload.php';
}

class Database {
    private static $instance = null;
    private $client;
    private $database;
    private $mockData = [];
    private $useFileStorage = true;
    private $dataFile;

    private function __construct() {
        $this->dataFile = __DIR__ . '/../data/mock_database.json';
        $this->connect();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function connect() {
        try {
            // Check if MongoDB extension is loaded
            if (extension_loaded('mongodb')) {
                $this->connectMongoDB();
            } else {
                if (APP_DEBUG) {
                    error_log("MongoDB extension not loaded. Using file storage.");
                }
                $this->connectFileStorage();
            }

        } catch (Exception $e) {
            error_log("Database connection failed: " . $e->getMessage());

            if (APP_DEBUG) {
                error_log("Falling back to file storage: " . $e->getMessage());
            }
            $this->connectFileStorage();
        }
    }

    private function connectMongoDB() {
        $uri = MONGODB_URI;
        if (empty($uri)) {
            throw new Exception('MongoDB URI is not configured. Please set MONGODB_URI in .env file.');
        }

        try {
            // Create MongoDB client using the extension
            $this->client = new MongoDB\Driver\Manager($uri);

            // Test connection with ping
            $command = new MongoDB\Driver\Command(['ping' => 1]);
            $this->client->executeCommand('admin', $command);

            $this->useFileStorage = false;

            if (APP_DEBUG) {
                error_log("MongoDB Atlas connected successfully to database: " . MONGODB_DATABASE);
            }

        } catch (Exception $e) {
            throw new Exception("MongoDB connection failed: " . $e->getMessage());
        }
    }

    private function connectFileStorage() {
        // Create data directory if it doesn't exist
        $dataDir = dirname($this->dataFile);
        if (!is_dir($dataDir)) {
            mkdir($dataDir, 0755, true);
        }

        // Load existing data
        if (file_exists($this->dataFile)) {
            $data = file_get_contents($this->dataFile);
            $this->mockData = json_decode($data, true) ?: [];
        }

        $this->useFileStorage = true;

        if (APP_DEBUG) {
            error_log("Using file storage for database operations: " . $this->dataFile);
        }
    }

    private function saveFileStorage() {
        if ($this->useFileStorage) {
            file_put_contents($this->dataFile, json_encode($this->mockData, JSON_PRETTY_PRINT));
        }
    }

    public function getDatabase() {
        return $this->database;
    }

    public function getCollection($collectionName) {
        if ($this->useFileStorage) {
            return $collectionName; // Just return collection name for file storage
        }
        return $this->database->selectCollection($collectionName);
    }

    // Helper method to insert document
    public function insertOne($collection, $document) {
        try {
            if ($this->useFileStorage) {
                return $this->insertOneFile($collection, $document);
            }

            $result = $this->getCollection($collection)->insertOne($document);
            return $result->getInsertedId();
        } catch (Exception $e) {
            error_log("Insert failed: " . $e->getMessage());
            return false;
        }
    }

    // Helper method to find documents
    public function find($collection, $filter = [], $options = []) {
        try {
            if ($this->useFileStorage) {
                return $this->findFile($collection, $filter, $options);
            }

            return $this->getCollection($collection)->find($filter, $options);
        } catch (Exception $e) {
            error_log("Find failed: " . $e->getMessage());
            return false;
        }
    }

    // Helper method to find one document
    public function findOne($collection, $filter = [], $options = []) {
        try {
            if ($this->useFileStorage) {
                return $this->findOneFile($collection, $filter, $options);
            }

            return $this->getCollection($collection)->findOne($filter, $options);
        } catch (Exception $e) {
            error_log("FindOne failed: " . $e->getMessage());
            return false;
        }
    }

    // Helper method to update document
    public function updateOne($collection, $filter, $update, $options = []) {
        try {
            if ($this->useFileStorage) {
                return $this->updateOneFile($collection, $filter, $update, $options);
            }

            return $this->getCollection($collection)->updateOne($filter, $update, $options);
        } catch (Exception $e) {
            error_log("Update failed: " . $e->getMessage());
            return false;
        }
    }

    // Helper method to delete document
    public function deleteOne($collection, $filter) {
        try {
            if ($this->useFileStorage) {
                return $this->deleteOneFile($collection, $filter);
            }

            return $this->getCollection($collection)->deleteOne($filter);
        } catch (Exception $e) {
            error_log("Delete failed: " . $e->getMessage());
            return false;
        }
    }

// Initialize database connection
try {
    $db = Database::getInstance();
} catch (Exception $e) {
    error_log("Failed to initialize database: " . $e->getMessage());
    if (APP_DEBUG) {
        echo "Database initialization failed: " . $e->getMessage();
    }
}
?>
