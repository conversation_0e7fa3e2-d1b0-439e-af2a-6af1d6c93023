<?php

namespace App\Models;

use Jen<PERSON>gers\Mongodb\Eloquent\Model;

class PatientRecord extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'patient_records';

    protected $fillable = [
        'patientId',
        'doctorId',
        'facilityId',
        'personalInfo',
        'medicalHistory',
        'allergies',
        'medications',
        'visitNotes',
        'prescriptions',
        'labResults',
        'vitals',
        'emergencyContact',
        'insurance',
        'createdAt',
        'updatedAt',
    ];

    protected $casts = [
        'personalInfo' => 'array',
        'medicalHistory' => 'array',
        'allergies' => 'array',
        'medications' => 'array',
        'visitNotes' => 'array',
        'prescriptions' => 'array',
        'labResults' => 'array',
        'vitals' => 'array',
        'emergencyContact' => 'array',
        'insurance' => 'array',
        'createdAt' => 'datetime',
        'updatedAt' => 'datetime',
    ];

    /**
     * Get the patient user
     */
    public function patient()
    {
        return User::find($this->patientId);
    }

    /**
     * Get the doctor user
     */
    public function doctor()
    {
        return User::find($this->doctorId);
    }

    /**
     * Get the facility
     */
    public function facility()
    {
        return HealthFacility::find($this->facilityId);
    }

    /**
     * Add visit note
     */
    public function addVisitNote($note, $doctorId)
    {
        $visitNotes = $this->visitNotes ?? [];
        $visitNotes[] = [
            'id' => (string) new \MongoDB\BSON\ObjectId(),
            'doctorId' => $doctorId,
            'note' => $note,
            'timestamp' => now(),
        ];

        $this->update(['visitNotes' => $visitNotes]);
    }

    /**
     * Add prescription
     */
    public function addPrescription($prescription, $doctorId)
    {
        $prescriptions = $this->prescriptions ?? [];
        $prescriptions[] = [
            'id' => (string) new \MongoDB\BSON\ObjectId(),
            'doctorId' => $doctorId,
            'medication' => $prescription['medication'],
            'dosage' => $prescription['dosage'],
            'frequency' => $prescription['frequency'],
            'duration' => $prescription['duration'],
            'instructions' => $prescription['instructions'] ?? '',
            'timestamp' => now(),
        ];

        $this->update(['prescriptions' => $prescriptions]);
    }

    /**
     * Encrypt sensitive data before saving
     */
    public function setPersonalInfoAttribute($value)
    {
        $this->attributes['personalInfo'] = encrypt($value);
    }

    /**
     * Decrypt sensitive data when retrieving
     */
    public function getPersonalInfoAttribute($value)
    {
        return $value ? decrypt($value) : null;
    }
}
