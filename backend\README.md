# AfyaSecure Backend API

Laravel-based REST API for the AfyaSecure Health Management System.

## Features

- **JWT Authentication** with role-based access control
- **MongoDB Database** for flexible document storage
- **RESTful API** with comprehensive endpoints
- **Role Management** (<PERSON><PERSON>, <PERSON>, <PERSON>min)
- **File Upload** support with AWS S3 integration
- **Activity Logging** for audit trails
- **Rate Limiting** for security
- **CORS** configured for frontend integration

## Requirements

- PHP 8.2+
- Composer
- MongoDB 4.4+
- Redis (optional, for caching)

## Installation

1. **Install Composer** (if not already installed):
   ```bash
   # Download and install Composer
   curl -sS https://getcomposer.org/installer | php
   sudo mv composer.phar /usr/local/bin/composer
   ```

2. **Install MongoDB** (if not already installed):
   ```bash
   # For Windows: Download from https://www.mongodb.com/try/download/community
   # For macOS: brew install mongodb-community
   # For Ubuntu: sudo apt install mongodb
   ```

3. **Install Dependencies**:
   ```bash
   cd backend
   composer install
   ```

4. **Environment Setup**:
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Configure Environment Variables**:
   Edit `.env` file with your settings:
   ```env
   DB_CONNECTION=mongodb
   DB_HOST=127.0.0.1
   DB_PORT=27017
   DB_DATABASE=afyasecure
   
   JWT_SECRET=your-jwt-secret-here
   ```

6. **Generate JWT Secret**:
   ```bash
   php artisan jwt:secret
   ```

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user
- `POST /api/auth/refresh` - Refresh JWT token

### Dashboard
- `GET /api/dashboard/stats` - Get dashboard statistics

### Patients
- `GET /api/patients` - List patients
- `POST /api/patients` - Create patient record
- `GET /api/patients/{id}` - Get patient details
- `PUT /api/patients/{id}` - Update patient
- `DELETE /api/patients/{id}` - Delete patient

### Appointments
- `GET /api/appointments` - List appointments
- `POST /api/appointments` - Create appointment
- `GET /api/appointments/{id}` - Get appointment details
- `PUT /api/appointments/{id}` - Update appointment
- `DELETE /api/appointments/{id}` - Cancel appointment
- `POST /api/appointments/{id}/status` - Update appointment status

### Facilities
- `GET /api/facilities` - List facilities
- `POST /api/facilities` - Create facility (Admin only)
- `GET /api/facilities/{id}` - Get facility details
- `PUT /api/facilities/{id}` - Update facility (Admin only)
- `GET /api/facilities/{id}/doctors` - Get facility doctors

### Billing
- `GET /api/billing` - List bills
- `POST /api/billing` - Create bill
- `GET /api/billing/{id}` - Get bill details
- `PUT /api/billing/{id}` - Update bill
- `POST /api/billing/{id}/approve` - Approve bill (Admin only)
- `GET /api/billing/{id}/invoice` - Generate invoice

### Admin
- `GET /api/admin/stats` - System statistics
- `GET /api/admin/users` - List all users
- `POST /api/admin/users` - Create user
- `PUT /api/admin/users/{id}` - Update user
- `POST /api/admin/users/{id}/verify` - Verify user
- `GET /api/admin/activity-logs` - Activity logs

## Models

### User
- Authentication and user management
- Roles: patient, doctor, admin
- Profile information and verification

### PatientRecord
- Medical history and patient data
- Visit notes and medical documents
- Doctor-patient relationships

### Appointment
- Appointment scheduling and management
- Status tracking and notifications
- Calendar integration

### Facility
- Healthcare facility information
- Location and services data
- Doctor associations

### Billing
- Invoice generation and management
- Payment tracking
- Approval workflows

### ActivityLog
- System activity tracking
- Audit trail for security
- User action logging

## Security Features

- **JWT Authentication** with refresh tokens
- **Rate Limiting** on API endpoints
- **Role-based Access Control**
- **Input Validation** and sanitization
- **CORS** protection
- **Activity Logging** for audit trails

## Development

### Running the Server
```bash
php artisan serve
# Server will start at http://localhost:8000
```

### Database Operations
```bash
# No migrations needed for MongoDB
# Collections are created automatically
```

### Testing
```bash
php artisan test
```

### Code Style
```bash
./vendor/bin/pint
```

## Production Deployment

1. **Environment Configuration**:
   - Set `APP_ENV=production`
   - Set `APP_DEBUG=false`
   - Configure proper database credentials
   - Set up SSL certificates

2. **Security**:
   - Use strong JWT secrets
   - Configure rate limiting
   - Set up proper CORS origins
   - Enable HTTPS

3. **Performance**:
   - Configure Redis for caching
   - Set up database indexing
   - Configure file storage (AWS S3)

## Troubleshooting

### Common Issues

1. **MongoDB Connection Error**:
   - Ensure MongoDB is running
   - Check connection credentials
   - Verify network connectivity

2. **JWT Token Issues**:
   - Generate new JWT secret
   - Check token expiration settings
   - Verify middleware configuration

3. **Permission Errors**:
   - Check file permissions
   - Verify storage directory access
   - Configure proper user roles

## Support

For issues and questions:
- Check the Laravel documentation
- Review MongoDB documentation
- Check JWT-Auth package documentation
