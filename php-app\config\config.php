<?php
/**
 * Configuration file for AfyaSecure PHP Application
 */

// Load environment variables from .env file
function loadEnv($path) {
    if (!file_exists($path)) {
        return;
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue; // Skip comments
        }
        
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        
        // Remove quotes if present
        if (preg_match('/^"(.*)"$/', $value, $matches)) {
            $value = $matches[1];
        }
        
        if (!array_key_exists($name, $_ENV)) {
            $_ENV[$name] = $value;
        }
    }
}

// Load environment variables
loadEnv(__DIR__ . '/../.env');

// Application Configuration
define('APP_NAME', $_ENV['APP_NAME'] ?? 'AfyaSecure PHP');
define('APP_ENV', $_ENV['APP_ENV'] ?? 'development');
define('APP_DEBUG', $_ENV['APP_DEBUG'] ?? true);
define('APP_URL', $_ENV['APP_URL'] ?? 'http://localhost');

// MongoDB Configuration
define('MONGODB_URI', $_ENV['MONGODB_URI'] ?? '');
define('MONGODB_DATABASE', $_ENV['MONGODB_DATABASE'] ?? 'afyasecure');

// Security Configuration
define('JWT_SECRET', $_ENV['JWT_SECRET'] ?? 'your-secret-key-change-this');
define('JWT_EXPIRY', $_ENV['JWT_EXPIRY'] ?? 3600); // 1 hour

// CORS Configuration
define('CORS_ALLOWED_ORIGINS', $_ENV['CORS_ALLOWED_ORIGINS'] ?? 'http://localhost:3000');

// File Upload Configuration
define('MAX_FILE_SIZE', $_ENV['MAX_FILE_SIZE'] ?? 10485760); // 10MB
define('UPLOAD_PATH', __DIR__ . '/../uploads/');

// Rate Limiting
define('RATE_LIMIT_REQUESTS', $_ENV['RATE_LIMIT_REQUESTS'] ?? 100);
define('RATE_LIMIT_WINDOW', $_ENV['RATE_LIMIT_WINDOW'] ?? 3600); // 1 hour

// Ensure upload directory exists
if (!is_dir(UPLOAD_PATH)) {
    mkdir(UPLOAD_PATH, 0755, true);
}
?>
