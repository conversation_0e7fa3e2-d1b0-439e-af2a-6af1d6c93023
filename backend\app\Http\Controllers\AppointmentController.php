<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Appointment;
use App\Models\User;
use App\Models\Facility;
use App\Models\ActivityLog;
use Carbon\Carbon;

class AppointmentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * Get appointments based on user role
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $query = Appointment::query();

        // Filter based on user role
        switch ($user->role) {
            case 'patient':
                $query->where('patientId', $user->_id);
                break;
            case 'doctor':
                $query->where('doctorId', $user->_id);
                break;
            case 'admin':
                // Admin can see all appointments
                break;
        }

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('date')) {
            $query->whereDate('date', $request->date);
        }

        if ($request->has('date_from') && $request->has('date_to')) {
            $query->whereBetween('date', [$request->date_from, $request->date_to]);
        }

        // Pagination
        $perPage = $request->get('per_page', 15);
        $appointments = $query->orderBy('date', 'desc')
                             ->orderBy('time', 'desc')
                             ->paginate($perPage);

        return response()->json($appointments);
    }

    /**
     * Create a new appointment
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'patientId' => 'required|string',
            'doctorId' => 'required|string',
            'facilityId' => 'required|string',
            'date' => 'required|date|after_or_equal:today',
            'time' => 'required|string',
            'type' => 'required|string',
            'reason' => 'required|string|max:500',
            'duration' => 'integer|min:15|max:180',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Verify patient, doctor, and facility exist
        $patient = User::find($request->patientId);
        $doctor = User::find($request->doctorId);
        $facility = Facility::find($request->facilityId);

        if (!$patient || $patient->role !== 'patient') {
            return response()->json(['error' => 'Invalid patient'], 400);
        }

        if (!$doctor || $doctor->role !== 'doctor') {
            return response()->json(['error' => 'Invalid doctor'], 400);
        }

        if (!$facility) {
            return response()->json(['error' => 'Invalid facility'], 400);
        }

        // Check if time slot is available
        $existingAppointment = Appointment::where('doctorId', $request->doctorId)
            ->where('date', $request->date)
            ->where('time', $request->time)
            ->where('status', 'scheduled')
            ->first();

        if ($existingAppointment) {
            return response()->json(['error' => 'Time slot is not available'], 409);
        }

        $appointment = Appointment::create([
            'patientId' => $request->patientId,
            'doctorId' => $request->doctorId,
            'facilityId' => $request->facilityId,
            'patientName' => $patient->firstName . ' ' . $patient->lastName,
            'doctorName' => $doctor->firstName . ' ' . $doctor->lastName,
            'facilityName' => $facility->name,
            'date' => $request->date,
            'time' => $request->time,
            'duration' => $request->duration ?? 30,
            'type' => $request->type,
            'status' => 'scheduled',
            'reason' => $request->reason,
            'notes' => $request->notes,
            'createdAt' => now(),
            'updatedAt' => now(),
        ]);

        // Log appointment creation
        ActivityLog::logActivity(
            'appointment_created',
            "New appointment scheduled for {$patient->firstName} {$patient->lastName}",
            [
                'appointment_id' => $appointment->_id,
                'patient_id' => $patient->_id,
                'doctor_id' => $doctor->_id,
                'date' => $request->date,
                'time' => $request->time,
            ]
        );

        return response()->json($appointment, 201);
    }

    /**
     * Get specific appointment
     */
    public function show($id)
    {
        $user = auth()->user();
        $appointment = Appointment::find($id);

        if (!$appointment) {
            return response()->json(['error' => 'Appointment not found'], 404);
        }

        // Check permissions
        if ($user->role === 'patient' && $appointment->patientId !== $user->_id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        if ($user->role === 'doctor' && $appointment->doctorId !== $user->_id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        return response()->json($appointment);
    }

    /**
     * Update appointment
     */
    public function update(Request $request, $id)
    {
        $appointment = Appointment::find($id);

        if (!$appointment) {
            return response()->json(['error' => 'Appointment not found'], 404);
        }

        $user = auth()->user();

        // Check permissions
        if ($user->role === 'patient' && $appointment->patientId !== $user->_id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        if ($user->role === 'doctor' && $appointment->doctorId !== $user->_id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'date' => 'date|after_or_equal:today',
            'time' => 'string',
            'type' => 'string',
            'reason' => 'string|max:500',
            'notes' => 'string|max:1000',
            'duration' => 'integer|min:15|max:180',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // If rescheduling, check availability
        if ($request->has('date') || $request->has('time')) {
            $newDate = $request->get('date', $appointment->date);
            $newTime = $request->get('time', $appointment->time);

            $conflictingAppointment = Appointment::where('doctorId', $appointment->doctorId)
                ->where('date', $newDate)
                ->where('time', $newTime)
                ->where('status', 'scheduled')
                ->where('_id', '!=', $appointment->_id)
                ->first();

            if ($conflictingAppointment) {
                return response()->json(['error' => 'Time slot is not available'], 409);
            }
        }

        $oldData = $appointment->toArray();
        $appointment->update($request->only([
            'date', 'time', 'type', 'reason', 'notes', 'duration'
        ]));

        // Log appointment update
        ActivityLog::logActivity(
            'appointment_updated',
            "Appointment updated for {$appointment->patientName}",
            [
                'appointment_id' => $appointment->_id,
                'old_data' => $oldData,
                'new_data' => $appointment->toArray(),
            ]
        );

        return response()->json($appointment);
    }

    /**
     * Delete appointment
     */
    public function destroy($id)
    {
        $appointment = Appointment::find($id);

        if (!$appointment) {
            return response()->json(['error' => 'Appointment not found'], 404);
        }

        $user = auth()->user();

        // Check permissions
        if ($user->role === 'patient' && $appointment->patientId !== $user->_id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        if ($user->role === 'doctor' && $appointment->doctorId !== $user->_id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Check if appointment can be cancelled
        if (!$appointment->canBeCancelled()) {
            return response()->json(['error' => 'Appointment cannot be cancelled'], 400);
        }

        $appointment->update(['status' => 'cancelled']);

        // Log appointment cancellation
        ActivityLog::logActivity(
            'appointment_cancelled',
            "Appointment cancelled for {$appointment->patientName}",
            [
                'appointment_id' => $appointment->_id,
                'cancelled_by' => $user->_id,
            ]
        );

        return response()->json(['message' => 'Appointment cancelled successfully']);
    }

    /**
     * Update appointment status
     */
    public function updateStatus(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:scheduled,completed,cancelled,rescheduled',
            'notes' => 'string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $appointment = Appointment::find($id);

        if (!$appointment) {
            return response()->json(['error' => 'Appointment not found'], 404);
        }

        $user = auth()->user();

        // Only doctors and admins can update status
        if (!in_array($user->role, ['doctor', 'admin'])) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        if ($user->role === 'doctor' && $appointment->doctorId !== $user->_id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $appointment->updateStatus($request->status, $request->notes);

        return response()->json($appointment);
    }

    /**
     * Get calendar view of appointments
     */
    public function getCalendar(Request $request, $date)
    {
        $user = auth()->user();
        
        if ($user->role !== 'doctor') {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $targetDate = Carbon::parse($date);
        
        $appointments = Appointment::where('doctorId', $user->_id)
            ->whereDate('date', $targetDate)
            ->orderBy('time')
            ->get();

        $availableSlots = Appointment::getAvailableSlots($user->_id, $targetDate->toDateString());

        return response()->json([
            'date' => $targetDate->toDateString(),
            'appointments' => $appointments,
            'available_slots' => $availableSlots,
        ]);
    }
}
