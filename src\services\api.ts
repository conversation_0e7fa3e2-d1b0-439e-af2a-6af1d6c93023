import axios from "axios"

const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:8000/api/v1"

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token")
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error),
)

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        const refreshToken = localStorage.getItem("refreshToken")
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refreshToken,
          })

          const { token } = response.data
          localStorage.setItem("token", token)

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${token}`
          return api(originalRequest)
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem("token")
        localStorage.removeItem("refreshToken")
        window.location.href = "/login"
      }
    }

    return Promise.reject(error)
  },
)

// Auth API
export const authAPI = {
  login: async (email: string, password: string) => {
    const response = await api.post("/auth/login", { email, password })
    return response.data
  },

  register: async (userData: any) => {
    const response = await api.post("/auth/register", userData)
    return response.data
  },

  refreshToken: async (refreshToken: string) => {
    const response = await api.post("/auth/refresh", { refreshToken })
    return response.data
  },

  getCurrentUser: async () => {
    const response = await api.get("/auth/me")
    return response.data
  },

  logout: async () => {
    const response = await api.post("/auth/logout")
    return response.data
  },
}

// Dashboard API
export const dashboardAPI = {
  getStats: async () => {
    const response = await api.get("/dashboard/stats")
    return response.data
  },
}

// Patient API
export const patientAPI = {
  getPatients: async (params?: any) => {
    const response = await api.get("/patients", { params })
    return response.data
  },

  getPatient: async (id: string) => {
    const response = await api.get(`/patients/${id}`)
    return response.data
  },

  createPatient: async (patientData: any) => {
    const response = await api.post("/patients", patientData)
    return response.data
  },

  updatePatient: async (id: string, patientData: any) => {
    const response = await api.put(`/patients/${id}`, patientData)
    return response.data
  },

  addVisitNote: async (id: string, noteData: any) => {
    const response = await api.post(`/patients/${id}/visit-notes`, noteData)
    return response.data
  },

  addPrescription: async (id: string, prescriptionData: any) => {
    const response = await api.post(`/patients/${id}/prescriptions`, prescriptionData)
    return response.data
  },

  uploadDocument: async (id: string, formData: FormData) => {
    const response = await api.post(`/patients/${id}/documents`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    })
    return response.data
  },
}

// Appointment API
export const appointmentAPI = {
  getAppointments: async (params?: any) => {
    const response = await api.get("/appointments", { params })
    return response.data
  },

  getAppointment: async (id: string) => {
    const response = await api.get(`/appointments/${id}`)
    return response.data
  },

  createAppointment: async (appointmentData: any) => {
    const response = await api.post("/appointments", appointmentData)
    return response.data
  },

  updateAppointment: async (id: string, appointmentData: any) => {
    const response = await api.put(`/appointments/${id}`, appointmentData)
    return response.data
  },

  updateStatus: async (id: string, status: string) => {
    const response = await api.patch(`/appointments/${id}/status`, { status })
    return response.data
  },

  getCalendar: async (date: string) => {
    const response = await api.get(`/appointments/calendar/${date}`)
    return response.data
  },
}

// Facility API
export const facilityAPI = {
  getFacilities: async (params?: any) => {
    const response = await api.get("/facilities", { params })
    return response.data
  },

  getFacility: async (id: string) => {
    const response = await api.get(`/facilities/${id}`)
    return response.data
  },

  searchFacilities: async (query: string) => {
    const response = await api.get(`/facilities/search/${query}`)
    return response.data
  },
}

// Billing API
export const billingAPI = {
  getBills: async (params?: any) => {
    const response = await api.get("/billing", { params })
    return response.data
  },

  getBill: async (id: string) => {
    const response = await api.get(`/billing/${id}`)
    return response.data
  },

  createBill: async (billData: any) => {
    const response = await api.post("/billing", billData)
    return response.data
  },

  approveBill: async (id: string) => {
    const response = await api.post(`/billing/${id}/approve`)
    return response.data
  },

  generateInvoice: async (id: string) => {
    const response = await api.get(`/billing/${id}/invoice`)
    return response.data
  },
}

// Admin API
export const adminAPI = {
  getStats: async () => {
    const response = await api.get("/admin/stats")
    return response.data
  },

  getUsers: async (params?: any) => {
    const response = await api.get("/admin/users", { params })
    return response.data
  },

  getActivityLogs: async (params?: any) => {
    const response = await api.get("/admin/activity-logs", { params })
    return response.data
  },

  getSystemHealth: async () => {
    const response = await api.get("/admin/system-health")
    return response.data
  },

  verifyUser: async (id: string) => {
    const response = await api.post(`/admin/users/${id}/verify`)
    return response.data
  },

  suspendUser: async (id: string) => {
    const response = await api.post(`/admin/users/${id}/suspend`)
    return response.data
  },
}

export default api
