<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Facility;
use App\Models\Appointment;
use App\Models\Billing;
use App\Models\ActivityLog;
use App\Models\PatientRecord;
use Carbon\Carbon;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
        $this->middleware(function ($request, $next) {
            if (auth()->user()->role !== 'admin') {
                return response()->json(['error' => 'Unauthorized'], 403);
            }
            return $next($request);
        });
    }

    /**
     * Get admin dashboard statistics
     */
    public function getStats()
    {
        $totalUsers = User::count();
        $totalPatients = User::where('role', 'patient')->count();
        $totalDoctors = User::where('role', 'doctor')->count();
        $totalFacilities = Facility::count();
        $totalAppointments = Appointment::count();
        $totalRevenue = Billing::where('status', 'paid')->sum('total');
        $pendingApprovals = Billing::where('status', 'pending')->count();

        // System health check
        $systemHealth = [
            'status' => 'healthy',
            'uptime' => $this->getSystemUptime(),
            'lastBackup' => $this->getLastBackupTime(),
        ];

        // Recent activity summary
        $recentActivity = ActivityLog::orderBy('timestamp', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'totalUsers' => $totalUsers,
            'totalPatients' => $totalPatients,
            'totalDoctors' => $totalDoctors,
            'totalFacilities' => $totalFacilities,
            'totalAppointments' => $totalAppointments,
            'totalRevenue' => $totalRevenue,
            'pendingApprovals' => $pendingApprovals,
            'systemHealth' => $systemHealth,
            'recentActivity' => $recentActivity,
        ]);
    }

    /**
     * Get all users with filtering and pagination
     */
    public function getUsers(Request $request)
    {
        $query = User::query();

        // Filter by role
        if ($request->has('role') && $request->role !== 'all') {
            $query->where('role', $request->role);
        }

        // Filter by verification status
        if ($request->has('verified')) {
            $query->where('verified', $request->boolean('verified'));
        }

        // Search by name or email
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('firstName', 'regex', "/$search/i")
                  ->orWhere('lastName', 'regex', "/$search/i")
                  ->orWhere('email', 'regex', "/$search/i");
            });
        }

        // Pagination
        $perPage = $request->get('per_page', 15);
        $users = $query->orderBy('createdAt', 'desc')
                      ->paginate($perPage);

        return response()->json($users);
    }

    /**
     * Create a new user
     */
    public function createUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8',
            'role' => 'required|in:patient,doctor,admin',
            'phone' => 'required|string|max:20',
            'dateOfBirth' => 'required|date',
            'specialization' => 'required_if:role,doctor|string|max:255',
            'medicalLicense' => 'required_if:role,doctor|string|max:100',
            'facilityId' => 'string|nullable',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $userData = [
            'firstName' => $request->firstName,
            'lastName' => $request->lastName,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'phone' => $request->phone,
            'dateOfBirth' => $request->dateOfBirth,
            'verified' => true, // Admin-created users are auto-verified
            'createdAt' => now(),
            'updatedAt' => now(),
        ];

        // Add doctor-specific fields
        if ($request->role === 'doctor') {
            $userData['specialization'] = $request->specialization;
            $userData['medicalLicense'] = $request->medicalLicense;
            $userData['facilityId'] = $request->facilityId;
        }

        $user = User::create($userData);

        // Log user creation
        ActivityLog::logActivity(
            'user_created_by_admin',
            "User {$user->firstName} {$user->lastName} created by admin",
            [
                'user_id' => $user->_id,
                'user_role' => $user->role,
                'created_by' => auth()->id(),
            ]
        );

        return response()->json($user, 201);
    }

    /**
     * Update user
     */
    public function updateUser(Request $request, $id)
    {
        $user = User::find($id);

        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'firstName' => 'string|max:255',
            'lastName' => 'string|max:255',
            'email' => 'string|email|max:255|unique:users,email,' . $id,
            'phone' => 'string|max:20',
            'role' => 'in:patient,doctor,admin',
            'verified' => 'boolean',
            'specialization' => 'string|max:255',
            'medicalLicense' => 'string|max:100',
            'facilityId' => 'string|nullable',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $oldData = $user->toArray();
        $user->update($request->only([
            'firstName', 'lastName', 'email', 'phone', 'role', 'verified',
            'specialization', 'medicalLicense', 'facilityId'
        ]));

        // Log user update
        ActivityLog::logActivity(
            'user_updated_by_admin',
            "User {$user->firstName} {$user->lastName} updated by admin",
            [
                'user_id' => $user->_id,
                'old_data' => $oldData,
                'new_data' => $user->toArray(),
                'updated_by' => auth()->id(),
            ]
        );

        return response()->json($user);
    }

    /**
     * Verify user
     */
    public function verifyUser($id)
    {
        $user = User::find($id);

        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        $user->update(['verified' => true]);

        // Log user verification
        ActivityLog::logActivity(
            'user_verified',
            "User {$user->firstName} {$user->lastName} verified",
            [
                'user_id' => $user->_id,
                'verified_by' => auth()->id(),
            ]
        );

        return response()->json(['message' => 'User verified successfully']);
    }

    /**
     * Suspend user
     */
    public function suspendUser($id)
    {
        $user = User::find($id);

        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        if ($user->role === 'admin') {
            return response()->json(['error' => 'Cannot suspend admin users'], 400);
        }

        $user->update(['verified' => false]);

        // Cancel future appointments for suspended users
        if ($user->role === 'patient') {
            Appointment::where('patientId', $user->_id)
                ->where('status', 'scheduled')
                ->where('date', '>=', now())
                ->update(['status' => 'cancelled']);
        } elseif ($user->role === 'doctor') {
            Appointment::where('doctorId', $user->_id)
                ->where('status', 'scheduled')
                ->where('date', '>=', now())
                ->update(['status' => 'cancelled']);
        }

        // Log user suspension
        ActivityLog::logActivity(
            'user_suspended',
            "User {$user->firstName} {$user->lastName} suspended",
            [
                'user_id' => $user->_id,
                'suspended_by' => auth()->id(),
            ]
        );

        return response()->json(['message' => 'User suspended successfully']);
    }

    /**
     * Delete user
     */
    public function deleteUser($id)
    {
        $user = User::find($id);

        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        if ($user->role === 'admin') {
            return response()->json(['error' => 'Cannot delete admin users'], 400);
        }

        // Check for dependencies
        $hasAppointments = Appointment::where(function ($query) use ($user) {
            $query->where('patientId', $user->_id)
                  ->orWhere('doctorId', $user->_id);
        })->exists();

        $hasBills = Billing::where(function ($query) use ($user) {
            $query->where('patientId', $user->_id)
                  ->orWhere('doctorId', $user->_id);
        })->exists();

        if ($hasAppointments || $hasBills) {
            return response()->json([
                'error' => 'Cannot delete user with existing appointments or bills'
            ], 400);
        }

        // Log user deletion
        ActivityLog::logActivity(
            'user_deleted',
            "User {$user->firstName} {$user->lastName} deleted",
            [
                'user_id' => $user->_id,
                'user_email' => $user->email,
                'deleted_by' => auth()->id(),
            ]
        );

        $user->delete();

        return response()->json(['message' => 'User deleted successfully']);
    }

    /**
     * Get activity logs
     */
    public function getActivityLogs(Request $request)
    {
        $query = ActivityLog::query();

        // Filter by user
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by action
        if ($request->has('action')) {
            $query->where('action', $request->action);
        }

        // Filter by date range
        if ($request->has('date_from') && $request->has('date_to')) {
            $query->whereBetween('timestamp', [$request->date_from, $request->date_to]);
        }

        // Pagination
        $perPage = $request->get('per_page', 50);
        $logs = $query->orderBy('timestamp', 'desc')
                     ->paginate($perPage);

        // Add user names to logs
        $logs->getCollection()->transform(function ($log) {
            $log->userName = $log->user_name;
            return $log;
        });

        return response()->json($logs);
    }

    /**
     * Get system health status
     */
    public function getSystemHealth()
    {
        $health = [
            'status' => 'healthy',
            'uptime' => $this->getSystemUptime(),
            'database' => $this->checkDatabaseHealth(),
            'storage' => $this->checkStorageHealth(),
            'memory' => $this->getMemoryUsage(),
            'lastBackup' => $this->getLastBackupTime(),
        ];

        // Determine overall status
        if ($health['database'] === 'error' || $health['storage'] === 'error') {
            $health['status'] = 'critical';
        } elseif ($health['memory'] > 80) {
            $health['status'] = 'warning';
        }

        return response()->json($health);
    }

    /**
     * Clean old activity logs
     */
    public function cleanLogs(Request $request)
    {
        $daysToKeep = $request->get('days', 90);
        $deletedCount = ActivityLog::cleanOldLogs($daysToKeep);

        // Log cleanup action
        ActivityLog::logActivity(
            'logs_cleaned',
            "Cleaned {$deletedCount} old activity logs",
            [
                'days_kept' => $daysToKeep,
                'deleted_count' => $deletedCount,
            ]
        );

        return response()->json([
            'message' => 'Logs cleaned successfully',
            'deleted_count' => $deletedCount
        ]);
    }

    /**
     * Helper methods
     */
    private function getSystemUptime()
    {
        // This would typically check server uptime
        // For now, return a placeholder
        return '7 days, 14 hours';
    }

    private function checkDatabaseHealth()
    {
        try {
            User::count();
            return 'healthy';
        } catch (\Exception $e) {
            return 'error';
        }
    }

    private function checkStorageHealth()
    {
        try {
            $freeSpace = disk_free_space(storage_path());
            $totalSpace = disk_total_space(storage_path());
            $usedPercentage = (($totalSpace - $freeSpace) / $totalSpace) * 100;
            
            if ($usedPercentage > 90) {
                return 'critical';
            } elseif ($usedPercentage > 80) {
                return 'warning';
            }
            
            return 'healthy';
        } catch (\Exception $e) {
            return 'error';
        }
    }

    private function getMemoryUsage()
    {
        return round(memory_get_usage(true) / 1024 / 1024, 2); // MB
    }

    private function getLastBackupTime()
    {
        // This would check your backup system
        // For now, return a placeholder
        return now()->subHours(6)->toDateTimeString();
    }
}
