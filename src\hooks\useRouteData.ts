"use client"

import { useLocation, useParams, useSearchParams } from "react-router-dom"
import { useMemo } from "react"

export const useRouteData = () => {
  const location = useLocation()
  const params = useParams()
  const [searchParams] = useSearchParams()

  const routeData = useMemo(() => {
    return {
      pathname: location.pathname,
      search: location.search,
      hash: location.hash,
      state: location.state,
      params,
      searchParams: Object.fromEntries(searchParams.entries()),
      isActive: (path: string) => location.pathname === path,
      isPartiallyActive: (path: string) => location.pathname.startsWith(path),
    }
  }, [location, params, searchParams])

  return routeData
}
