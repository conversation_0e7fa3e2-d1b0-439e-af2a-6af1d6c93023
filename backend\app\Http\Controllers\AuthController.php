<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\RateLimiter;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use Tymon\JWTAuth\Exceptions\JWTException;

class AuthController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api', ['except' => ['login', 'register', 'refresh']]);
    }

    /**
     * Login user and return JWT tokens
     */
    public function login(Request $request)
    {
        $key = 'login.' . $request->ip();
        
        if (RateLimiter::tooManyAttempts($key, 5)) {
            return response()->json([
                'error' => 'Too many login attempts. Please try again later.'
            ], 429);
        }

        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:8',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $credentials = $request->only('email', 'password');

        try {
            if (!$token = JWTAuth::attempt($credentials)) {
                RateLimiter::hit($key, 300); // 5 minutes lockout
                return response()->json(['error' => 'Invalid credentials'], 401);
            }
        } catch (JWTException $e) {
            return response()->json(['error' => 'Could not create token'], 500);
        }

        $user = auth()->user();
        $refreshToken = $this->generateRefreshToken($user);

        // Log successful login
        ActivityLog::create([
            'user_id' => $user->_id,
            'action' => 'login',
            'description' => 'User logged in successfully',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now(),
        ]);

        RateLimiter::clear($key);

        return response()->json([
            'user' => $user,
            'token' => $token,
            'refreshToken' => $refreshToken,
            'expiresIn' => config('jwt.ttl') * 60
        ]);
    }

    /**
     * Register a new user
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:patient,doctor,admin',
            'phone' => 'required|string|max:20',
            'dateOfBirth' => 'required|date',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = User::create([
            'firstName' => $request->firstName,
            'lastName' => $request->lastName,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'phone' => $request->phone,
            'dateOfBirth' => $request->dateOfBirth,
            'verified' => false,
            'createdAt' => now(),
            'updatedAt' => now(),
        ]);

        // Generate verification QR code
        $this->generateVerificationQR($user);

        $token = JWTAuth::fromUser($user);
        $refreshToken = $this->generateRefreshToken($user);

        // Log registration
        ActivityLog::create([
            'user_id' => $user->_id,
            'action' => 'register',
            'description' => 'New user registered',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now(),
        ]);

        return response()->json([
            'user' => $user,
            'token' => $token,
            'refreshToken' => $refreshToken,
            'expiresIn' => config('jwt.ttl') * 60
        ], 201);
    }

    /**
     * Refresh JWT token
     */
    public function refresh(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'refreshToken' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $refreshToken = $request->refreshToken;
            $user = User::where('refreshToken', $refreshToken)->first();

            if (!$user || $user->refreshTokenExpiry < now()) {
                return response()->json(['error' => 'Invalid refresh token'], 401);
            }

            $newToken = JWTAuth::fromUser($user);
            $newRefreshToken = $this->generateRefreshToken($user);

            return response()->json([
                'token' => $newToken,
                'refreshToken' => $newRefreshToken,
                'expiresIn' => config('jwt.ttl') * 60
            ]);
        } catch (JWTException $e) {
            return response()->json(['error' => 'Could not refresh token'], 500);
        }
    }

    /**
     * Get current authenticated user
     */
    public function me()
    {
        return response()->json(auth()->user());
    }

    /**
     * Logout user
     */
    public function logout(Request $request)
    {
        $user = auth()->user();

        // Log logout
        ActivityLog::create([
            'user_id' => $user->_id,
            'action' => 'logout',
            'description' => 'User logged out',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now(),
        ]);

        // Invalidate refresh token
        $user->update([
            'refreshToken' => null,
            'refreshTokenExpiry' => null,
        ]);

        JWTAuth::invalidate(JWTAuth::getToken());

        return response()->json(['message' => 'Successfully logged out']);
    }

    /**
     * Generate refresh token for user
     */
    private function generateRefreshToken($user)
    {
        $refreshToken = bin2hex(random_bytes(64));
        $expiry = now()->addDays(30); // 30 days expiry

        $user->update([
            'refreshToken' => $refreshToken,
            'refreshTokenExpiry' => $expiry,
        ]);

        return $refreshToken;
    }

    /**
     * Generate verification QR code
     */
    private function generateVerificationQR($user)
    {
        $qrData = [
            'id' => $user->_id,
            'name' => $user->firstName . ' ' . $user->lastName,
            'role' => $user->role,
            'verified' => $user->verified,
            'signature' => hash_hmac('sha256', $user->_id . $user->email, config('app.key'))
        ];

        $user->update([
            'qrCode' => base64_encode(json_encode($qrData))
        ]);
    }
}
