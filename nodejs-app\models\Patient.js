import mongoose from 'mongoose';

const patientSchema = new mongoose.Schema({
    patientNumber: {
        type: String,
        unique: true,
        required: true,
        trim: true
    },
    firstName: {
        type: String,
        required: [true, 'First name is required'],
        trim: true,
        maxlength: [50, 'First name cannot exceed 50 characters']
    },
    lastName: {
        type: String,
        required: [true, 'Last name is required'],
        trim: true,
        maxlength: [50, 'Last name cannot exceed 50 characters']
    },
    middleName: {
        type: String,
        trim: true,
        maxlength: [50, 'Middle name cannot exceed 50 characters']
    },
    dateOfBirth: {
        type: Date,
        required: [true, 'Date of birth is required'],
        validate: {
            validator: function(value) {
                return value < new Date();
            },
            message: 'Date of birth must be in the past'
        }
    },
    gender: {
        type: String,
        required: [true, 'Gender is required'],
        enum: ['male', 'female', 'other'],
        lowercase: true
    },
    email: {
        type: String,
        trim: true,
        lowercase: true,
        sparse: true, // Allow multiple null values but unique non-null values
        match: [
            /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
            'Please enter a valid email address'
        ]
    },
    phone: {
        type: String,
        required: [true, 'Phone number is required'],
        trim: true,
        match: [/^\+?[\d\s\-\(\)]+$/, 'Please enter a valid phone number']
    },
    alternatePhone: {
        type: String,
        trim: true,
        match: [/^\+?[\d\s\-\(\)]+$/, 'Please enter a valid phone number']
    },
    address: {
        street: {
            type: String,
            trim: true,
            maxlength: [200, 'Street address cannot exceed 200 characters']
        },
        city: {
            type: String,
            trim: true,
            maxlength: [100, 'City cannot exceed 100 characters']
        },
        state: {
            type: String,
            trim: true,
            maxlength: [100, 'State cannot exceed 100 characters']
        },
        zipCode: {
            type: String,
            trim: true,
            maxlength: [20, 'Zip code cannot exceed 20 characters']
        },
        country: {
            type: String,
            trim: true,
            maxlength: [100, 'Country cannot exceed 100 characters'],
            default: 'Kenya'
        }
    },
    nationalId: {
        type: String,
        trim: true,
        sparse: true,
        maxlength: [50, 'National ID cannot exceed 50 characters']
    },
    maritalStatus: {
        type: String,
        enum: ['single', 'married', 'divorced', 'widowed', 'other'],
        lowercase: true
    },
    occupation: {
        type: String,
        trim: true,
        maxlength: [100, 'Occupation cannot exceed 100 characters']
    },
    bloodType: {
        type: String,
        enum: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
        uppercase: true
    },
    emergencyContacts: [{
        name: {
            type: String,
            required: true,
            trim: true,
            maxlength: [100, 'Emergency contact name cannot exceed 100 characters']
        },
        relationship: {
            type: String,
            required: true,
            trim: true,
            maxlength: [50, 'Relationship cannot exceed 50 characters']
        },
        phone: {
            type: String,
            required: true,
            trim: true,
            match: [/^\+?[\d\s\-\(\)]+$/, 'Please enter a valid phone number']
        },
        email: {
            type: String,
            trim: true,
            lowercase: true,
            match: [
                /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
                'Please enter a valid email address'
            ]
        }
    }],
    medicalHistory: [{
        condition: {
            type: String,
            required: true,
            trim: true
        },
        diagnosedDate: {
            type: Date,
            required: true
        },
        status: {
            type: String,
            enum: ['active', 'resolved', 'chronic'],
            default: 'active'
        },
        notes: {
            type: String,
            trim: true
        },
        addedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        addedAt: {
            type: Date,
            default: Date.now
        }
    }],
    allergies: [{
        allergen: {
            type: String,
            required: true,
            trim: true
        },
        reaction: {
            type: String,
            required: true,
            trim: true
        },
        severity: {
            type: String,
            enum: ['mild', 'moderate', 'severe'],
            required: true
        },
        notes: {
            type: String,
            trim: true
        },
        addedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        addedAt: {
            type: Date,
            default: Date.now
        }
    }],
    medications: [{
        name: {
            type: String,
            required: true,
            trim: true
        },
        dosage: {
            type: String,
            required: true,
            trim: true
        },
        frequency: {
            type: String,
            required: true,
            trim: true
        },
        startDate: {
            type: Date,
            required: true
        },
        endDate: {
            type: Date
        },
        prescribedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        status: {
            type: String,
            enum: ['active', 'completed', 'discontinued'],
            default: 'active'
        },
        notes: {
            type: String,
            trim: true
        }
    }],
    insurance: {
        provider: {
            type: String,
            trim: true
        },
        policyNumber: {
            type: String,
            trim: true
        },
        groupNumber: {
            type: String,
            trim: true
        },
        expiryDate: {
            type: Date
        }
    },
    status: {
        type: String,
        enum: ['active', 'inactive', 'deceased'],
        default: 'active'
    },
    registeredBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    lastVisit: {
        type: Date
    },
    notes: {
        type: String,
        trim: true
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes
patientSchema.index({ patientNumber: 1 });
patientSchema.index({ firstName: 1, lastName: 1 });
patientSchema.index({ email: 1 });
patientSchema.index({ phone: 1 });
patientSchema.index({ nationalId: 1 });
patientSchema.index({ status: 1 });
patientSchema.index({ createdAt: -1 });

// Virtual for full name
patientSchema.virtual('fullName').get(function() {
    const parts = [this.firstName];
    if (this.middleName) parts.push(this.middleName);
    parts.push(this.lastName);
    return parts.join(' ');
});

// Virtual for age
patientSchema.virtual('age').get(function() {
    if (!this.dateOfBirth) return null;
    const today = new Date();
    const birthDate = new Date(this.dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }
    
    return age;
});

// Pre-save middleware to generate patient number
patientSchema.pre('save', async function(next) {
    if (this.isNew && !this.patientNumber) {
        const year = new Date().getFullYear();
        const count = await this.constructor.countDocuments({
            createdAt: {
                $gte: new Date(year, 0, 1),
                $lt: new Date(year + 1, 0, 1)
            }
        });
        
        this.patientNumber = `P${year}${String(count + 1).padStart(4, '0')}`;
    }
    next();
});

// Static method to search patients
patientSchema.statics.search = function(query, options = {}) {
    const searchRegex = new RegExp(query, 'i');
    const filter = {
        $or: [
            { firstName: searchRegex },
            { lastName: searchRegex },
            { patientNumber: searchRegex },
            { email: searchRegex },
            { phone: searchRegex },
            { nationalId: searchRegex }
        ]
    };
    
    if (options.status) {
        filter.status = options.status;
    }
    
    return this.find(filter)
        .populate('registeredBy', 'name email')
        .sort({ createdAt: -1 })
        .limit(options.limit || 50);
};

// Static method to find active patients
patientSchema.statics.findActive = function() {
    return this.find({ status: 'active' });
};

const Patient = mongoose.model('Patient', patientSchema);

export default Patient;
