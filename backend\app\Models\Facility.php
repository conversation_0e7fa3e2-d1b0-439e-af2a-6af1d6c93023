<?php

namespace App\Models;

use Jenssegers\Mongodb\Eloquent\Model;

class Facility extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'facilities';

    protected $fillable = [
        'name',
        'type',
        'description',
        'address',
        'phone',
        'email',
        'website',
        'services',
        'operatingHours',
        'coordinates',
        'rating',
        'reviewCount',
        'verified',
        'licenseNumber',
        'accreditation',
        'emergencyServices',
        'parkingAvailable',
        'wheelchairAccessible',
        'insuranceAccepted',
        'languages',
        'photos',
        'createdAt',
        'updatedAt',
    ];

    protected $casts = [
        'address' => 'array',
        'services' => 'array',
        'operatingHours' => 'array',
        'coordinates' => 'array',
        'rating' => 'float',
        'reviewCount' => 'integer',
        'verified' => 'boolean',
        'emergencyServices' => 'boolean',
        'parkingAvailable' => 'boolean',
        'wheelchairAccessible' => 'boolean',
        'insuranceAccepted' => 'array',
        'languages' => 'array',
        'photos' => 'array',
        'createdAt' => 'datetime',
        'updatedAt' => 'datetime',
    ];

    /**
     * Get doctors associated with this facility
     */
    public function doctors()
    {
        return $this->hasMany(User::class, 'facilityId', '_id')
                    ->where('role', 'doctor');
    }

    /**
     * Get appointments at this facility
     */
    public function appointments()
    {
        return $this->hasMany(Appointment::class, 'facilityId', '_id');
    }

    /**
     * Get patient records from this facility
     */
    public function patientRecords()
    {
        return $this->hasMany(PatientRecord::class, 'facilityId', '_id');
    }

    /**
     * Scope for verified facilities
     */
    public function scopeVerified($query)
    {
        return $query->where('verified', true);
    }

    /**
     * Scope for facilities by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for facilities with specific service
     */
    public function scopeWithService($query, $service)
    {
        return $query->where('services', 'regex', "/$service/i");
    }

    /**
     * Scope for facilities within radius (requires coordinates)
     */
    public function scopeWithinRadius($query, $latitude, $longitude, $radiusKm = 10)
    {
        return $query->where('coordinates', 'near', [
            '$geometry' => [
                'type' => 'Point',
                'coordinates' => [(float)$longitude, (float)$latitude]
            ],
            '$maxDistance' => $radiusKm * 1000 // Convert km to meters
        ]);
    }

    /**
     * Search facilities by name, services, or location
     */
    public function scopeSearch($query, $searchTerm)
    {
        return $query->where(function ($q) use ($searchTerm) {
            $q->where('name', 'regex', "/$searchTerm/i")
              ->orWhere('services', 'regex', "/$searchTerm/i")
              ->orWhere('address.city', 'regex', "/$searchTerm/i")
              ->orWhere('address.state', 'regex', "/$searchTerm/i");
        });
    }

    /**
     * Get facility's full address as string
     */
    public function getFullAddressAttribute()
    {
        $address = $this->address;
        if (!$address) return '';

        return implode(', ', array_filter([
            $address['street'] ?? '',
            $address['city'] ?? '',
            $address['state'] ?? '',
            $address['zipCode'] ?? '',
        ]));
    }

    /**
     * Check if facility is currently open
     */
    public function getIsOpenAttribute()
    {
        $operatingHours = $this->operatingHours;
        if (!$operatingHours) return false;

        $currentDay = strtolower(now()->format('l'));
        $currentTime = now()->format('H:i');

        if (!isset($operatingHours[$currentDay])) return false;

        $dayHours = $operatingHours[$currentDay];
        if (isset($dayHours['closed']) && $dayHours['closed']) return false;

        $openTime = $dayHours['open'] ?? '00:00';
        $closeTime = $dayHours['close'] ?? '23:59';

        return $currentTime >= $openTime && $currentTime <= $closeTime;
    }

    /**
     * Get available doctors at this facility
     */
    public function getAvailableDoctorsAttribute()
    {
        return $this->doctors()
                    ->where('verified', true)
                    ->get()
                    ->map(function ($doctor) {
                        return [
                            'id' => $doctor->_id,
                            'name' => $doctor->firstName . ' ' . $doctor->lastName,
                            'specialization' => $doctor->specialization,
                            'available' => $this->isDoctorAvailable($doctor),
                        ];
                    });
    }

    /**
     * Check if a doctor is currently available
     */
    private function isDoctorAvailable($doctor)
    {
        // Check if doctor has any appointments in the next hour
        $nextHour = now()->addHour();
        
        $hasAppointment = Appointment::where('doctorId', $doctor->_id)
            ->where('date', now()->toDateString())
            ->where('time', '>=', now()->format('H:i'))
            ->where('time', '<=', $nextHour->format('H:i'))
            ->where('status', 'scheduled')
            ->exists();

        return !$hasAppointment;
    }

    /**
     * Calculate distance from given coordinates
     */
    public function getDistanceFrom($latitude, $longitude)
    {
        if (!$this->coordinates) return null;

        $facilityLat = $this->coordinates['lat'] ?? 0;
        $facilityLng = $this->coordinates['lng'] ?? 0;

        // Haversine formula
        $earthRadius = 6371; // km

        $dLat = deg2rad($latitude - $facilityLat);
        $dLng = deg2rad($longitude - $facilityLng);

        $a = sin($dLat/2) * sin($dLat/2) +
             cos(deg2rad($facilityLat)) * cos(deg2rad($latitude)) *
             sin($dLng/2) * sin($dLng/2);

        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
        $distance = $earthRadius * $c;

        return round($distance, 2);
    }

    /**
     * Add a review/rating to the facility
     */
    public function addReview($rating, $review = null, $userId = null)
    {
        // Update average rating and review count
        $currentRating = $this->rating ?? 0;
        $currentCount = $this->reviewCount ?? 0;
        
        $newCount = $currentCount + 1;
        $newRating = (($currentRating * $currentCount) + $rating) / $newCount;

        $this->update([
            'rating' => round($newRating, 1),
            'reviewCount' => $newCount,
        ]);

        // Store individual review (you might want a separate reviews collection)
        // Review::create([...]);

        return $this;
    }

    /**
     * Get facility statistics
     */
    public function getStats()
    {
        return [
            'total_doctors' => $this->doctors()->count(),
            'total_appointments' => $this->appointments()->count(),
            'appointments_this_month' => $this->appointments()
                ->whereMonth('date', now()->month)
                ->whereYear('date', now()->year)
                ->count(),
            'patient_records' => $this->patientRecords()->count(),
            'average_rating' => $this->rating,
            'total_reviews' => $this->reviewCount,
        ];
    }
}
